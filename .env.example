# PazarYeri Price Tracker Environment Variables
# Copy this file to .env and update with your values

# Database Configuration
PAZARYERI_DB_PATH=data/pazaryeri.db
PAZARYERI_DB_TIMEOUT=30

# Scraping Configuration
PAZARYERI_HEADLESS=true
PAZARYERI_TIMEOUT=30000
PAZARYERI_RETRY_COUNT=3
PAZARYERI_RATE_LIMIT_DELAY=2

# Logging Configuration
PAZARYERI_LOG_LEVEL=INFO
PAZARYERI_LOG_FILE=data/logs/pazaryeri.log

# Application Configuration
PAZARYERI_CONFIG_PATH=config/config.json

# API Keys (if needed in future)
# PAZARYERI_API_KEY=your-api-key-here

# Proxy Configuration (optional)
# PAZARYERI_PROXY_URL=http://proxy.example.com:8080
# PAZARYERI_PROXY_USERNAME=username
# PAZARYERI_PROXY_PASSWORD=password

# Notification Settings (optional)
# PAZARYERI_SMTP_HOST=smtp.gmail.com
# PAZARYERI_SMTP_PORT=587
# PAZARYERI_SMTP_USERNAME=<EMAIL>
# PAZARYERI_SMTP_PASSWORD=your-app-password
# PAZARYERI_NOTIFICATION_EMAIL=<EMAIL>

# Performance Settings
PAZARYERI_MAX_WORKERS=4
PAZARYERI_BATCH_SIZE=10

# Feature Flags
PAZARYERI_ENABLE_SELENIUM_FALLBACK=true
PAZARYERI_ENABLE_SCREENSHOTS=false
PAZARYERI_ENABLE_PRICE_HISTORY=true

# Development Settings
PAZARYERI_DEBUG=false
PAZARYERI_PROFILE=false