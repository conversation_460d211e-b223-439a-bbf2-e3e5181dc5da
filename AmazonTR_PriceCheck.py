"""
Amazon TR Supplier Service - Amazon Türkiye sitesinden fiyat ve ürün bilgisi çekme servisi
"""

import asyncio
import logging
from typing import Optional, Dict, Any
from playwright.async_api import TimeoutError as PlaywrightTimeoutError
from base_supplier_service import SupplierService

logger = logging.getLogger(__name__)

class AmazonTRService(SupplierService):
    """Amazon TR için özelleştirilmiş supplier service"""
    
    def __init__(self):
        super().__init__("Amazon TR", "https://www.amazon.com.tr")
    
    async def extract_price(self, url: str) -> Optional[float]:
        """Amazon TR URL'sinden fiyat çeker"""
        try:
            await self.page.goto(url, timeout=60000)
            await self.handle_cookie_consent()
            await self.wait_for_page_load()
            
            # Amazon fiyat seçicileri
            price_selectors = [
                '.a-price-current .a-offscreen',
                '.a-price .a-offscreen',
                '#priceblock_dealprice',
                '#priceblock_ourprice',
                '.a-price-range .a-offscreen',
                '.a-price-whole'
            ]
            
            for selector in price_selectors:
                try:
                    price_element = self.page.locator(selector).first
                    await price_element.wait_for(state='visible', timeout=10000)
                    price_text = await price_element.inner_text()
                    
                    if price_text:
                        price = self.clean_price_text(price_text)
                        if price:
                            logger.info(f"Amazon TR fiyat bulundu: {price} TL - {url}")
                            return price
                            
                except PlaywrightTimeoutError:
                    continue
                except Exception as e:
                    logger.debug(f"Fiyat seçici hatası ({selector}): {e}")
                    continue
            
            logger.warning(f"Amazon TR fiyat bulunamadı: {url}")
            return None
            
        except Exception as e:
            logger.error(f"Amazon TR fiyat çekme hatası: {url} -> {e}")
            return None
    
    async def extract_product_details(self, url: str) -> Optional[Dict[str, Any]]:
        """Amazon TR URL'sinden ürün detaylarını çeker"""
        try:
            await self.page.goto(url, timeout=60000)
            await self.handle_cookie_consent()
            await self.wait_for_page_load()
            
            details = {}
            
            # Ürün adı
            product_name_selectors = [
                '#productTitle',
                'h1.a-size-large',
                '.product-title'
            ]
            
            for selector in product_name_selectors:
                try:
                    element = self.page.locator(selector).first
                    await element.wait_for(state='visible', timeout=10000)
                    details['product_name'] = await element.inner_text()
                    break
                except PlaywrightTimeoutError:
                    continue
            
            # Marka adı
            brand_selectors = [
                '#bylineInfo',
                '.a-link-normal[data-brand]',
                '.po-brand .po-break-word'
            ]
            
            for selector in brand_selectors:
                try:
                    element = self.page.locator(selector).first
                    await element.wait_for(state='visible', timeout=10000)
                    brand_text = await element.inner_text()
                    # "Marka: " gibi prefix'leri temizle
                    details['brand_name'] = brand_text.replace('Marka:', '').strip()
                    break
                except PlaywrightTimeoutError:
                    continue
            
            # Fiyat
            details['price'] = await self.extract_price(url)
            
            # Stok durumu
            try:
                add_to_cart = self.page.locator('#add-to-cart-button').first
                await add_to_cart.wait_for(state='visible', timeout=5000)
                details['availability'] = 'in_stock'
            except:
                details['availability'] = 'out_of_stock'
            
            if details.get('product_name') and details.get('brand_name'):
                logger.info(f"Amazon TR ürün detayları çekildi: {details['product_name']}")
                return details
            else:
                logger.warning(f"Amazon TR ürün detayları eksik: {url}")
                return None
                
        except Exception as e:
            logger.error(f"Amazon TR ürün detayları çekme hatası: {url} -> {e}")
            return None

async def main():
    """Amazon TR için ana işlem fonksiyonu"""
    async with AmazonTRService() as service:
        await service.process_all_products()

if __name__ == '__main__':
    asyncio.run(main())
