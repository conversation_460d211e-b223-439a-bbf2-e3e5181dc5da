
"""
Hepsiburada Supplier Service - Hepsiburada sitesinden fiyat ve ürün bilgisi çekme servisi
"""

import asyncio
import logging
from typing import Optional, Dict, Any
from playwright.async_api import TimeoutError as PlaywrightTimeoutError
from base_supplier_service import SupplierService

logger = logging.getLogger(__name__)

class HepsiburadaService(SupplierService):
    """Hepsiburada için özelleştirilmiş supplier service"""

    def __init__(self):
        super().__init__("Hepsiburada", "https://www.hepsiburada.com")

    async def extract_price(self, url: str) -> Optional[float]:
        """Hepsiburada URL'sinden fiyat çeker"""
        try:
            await self.page.goto(url, timeout=60000)
            await self.handle_cookie_consent()
            await self.wait_for_page_load()

            # Hepsiburada fiyat seçicileri (öncelik sırasına göre)
            price_selectors = [
                '[data-test-id="price-current-price"]',
                '.price-value',
                '.product-price .price-value',
                '[data-testid="price-current-price"]',
                '.currentPrice',
                '.price-current',
                '.product-price-container .price',
                'span[data-test-id="price-current-price"]'
            ]

            for selector in price_selectors:
                try:
                    price_element = self.page.locator(selector).first
                    await price_element.wait_for(state='visible', timeout=10000)
                    price_text = await price_element.inner_text()

                    if price_text:
                        price = self.clean_price_text(price_text)
                        if price:
                            logger.info(f"Hepsiburada fiyat bulundu: {price} TL - {url}")
                            return price

                except PlaywrightTimeoutError:
                    continue
                except Exception as e:
                    logger.debug(f"Fiyat seçici hatası ({selector}): {e}")
                    continue

            logger.warning(f"Hepsiburada fiyat bulunamadı: {url}")
            return None

        except Exception as e:
            logger.error(f"Hepsiburada fiyat çekme hatası: {url} -> {e}")
            return None

    async def extract_product_details(self, url: str) -> Optional[Dict[str, Any]]:
        """Hepsiburada URL'sinden ürün detaylarını çeker"""
        try:
            await self.page.goto(url, timeout=60000)
            await self.handle_cookie_consent()
            await self.wait_for_page_load()

            details = {}

            # Ürün adı
            product_name_selectors = [
                'h1[data-test-id="product-name"]',
                'h1.product-name',
                '.product-name h1',
                'h1[id="product-name"]'
            ]

            for selector in product_name_selectors:
                try:
                    element = self.page.locator(selector).first
                    await element.wait_for(state='visible', timeout=10000)
                    details['product_name'] = await element.inner_text()
                    break
                except PlaywrightTimeoutError:
                    continue

            # Marka adı
            brand_selectors = [
                '[data-test-id="product-brand"]',
                '.product-brand',
                '.brand-name',
                'a[data-test-id="product-brand-name"]'
            ]

            for selector in brand_selectors:
                try:
                    element = self.page.locator(selector).first
                    await element.wait_for(state='visible', timeout=10000)
                    details['brand_name'] = await element.inner_text()
                    break
                except PlaywrightTimeoutError:
                    continue

            # Fiyat
            details['price'] = await self.extract_price(url)

            # Stok durumu
            try:
                stock_element = self.page.locator('[data-test-id="product-stock-status"]').first
                stock_text = await stock_element.inner_text()
                details['availability'] = 'in_stock' if 'stokta' in stock_text.lower() else 'out_of_stock'
            except:
                details['availability'] = 'unknown'

            if details.get('product_name') and details.get('brand_name'):
                logger.info(f"Hepsiburada ürün detayları çekildi: {details['product_name']}")
                return details
            else:
                logger.warning(f"Hepsiburada ürün detayları eksik: {url}")
                return None

        except Exception as e:
            logger.error(f"Hepsiburada ürün detayları çekme hatası: {url} -> {e}")
            return None

    async def handle_cookie_consent(self):
        """Hepsiburada özel çerez onayı"""
        try:
            # Hepsiburada çerez onayı butonları
            cookie_selectors = [
                'button[data-test-id="cookie-accept"]',
                '#cookieUsageAcceptButton',
                'button:has-text("Kabul Et")',
                'button:has-text("Tamam")',
                '.cookie-accept-button'
            ]

            for selector in cookie_selectors:
                try:
                    await self.page.locator(selector).click(timeout=3000)
                    logger.info(f"Hepsiburada çerez onayı tıklandı: {selector}")
                    break
                except PlaywrightTimeoutError:
                    continue

        except Exception as e:
            logger.debug(f"Hepsiburada çerez onayı handle edilemedi: {e}")

async def main():
    """Hepsiburada için ana işlem fonksiyonu"""
    async with HepsiburadaService() as service:
        await service.process_all_products()

if __name__ == '__main__':
    asyncio.run(main())
