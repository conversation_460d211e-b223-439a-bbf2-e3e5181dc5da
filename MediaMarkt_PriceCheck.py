"""
MediaMarkt Supplier Service - MediaMarkt sitesinden fiyat ve ürün bilgisi çekme servisi
"""

import asyncio
import logging
from typing import Optional, Dict, Any
from playwright.async_api import TimeoutError as PlaywrightTimeoutError
from base_supplier_service import SupplierService

logger = logging.getLogger(__name__)

class MediaMarktService(SupplierService):
    """MediaMarkt için özelleştirilmiş supplier service"""
    
    def __init__(self):
        super().__init__("MediaMarkt", "https://www.mediamarkt.com.tr")
    
    async def extract_price(self, url: str) -> Optional[float]:
        """MediaMarkt URL'sinden fiyat çeker"""
        try:
            await self.page.goto(url, timeout=60000)
            await self.handle_cookie_consent()
            await self.wait_for_page_load()
            
            # MediaMarkt fiyat seçicileri
            price_selectors = [
                '.price-current',
                '.product-price .current',
                '[data-testid="mms-price-current"]',
                '.price-value',
                '.current-price',
                '.product-price-container .price'
            ]
            
            for selector in price_selectors:
                try:
                    price_element = self.page.locator(selector).first
                    await price_element.wait_for(state='visible', timeout=10000)
                    price_text = await price_element.inner_text()
                    
                    if price_text:
                        price = self.clean_price_text(price_text)
                        if price:
                            logger.info(f"MediaMarkt fiyat bulundu: {price} TL - {url}")
                            return price
                            
                except PlaywrightTimeoutError:
                    continue
                except Exception as e:
                    logger.debug(f"Fiyat seçici hatası ({selector}): {e}")
                    continue
            
            logger.warning(f"MediaMarkt fiyat bulunamadı: {url}")
            return None
            
        except Exception as e:
            logger.error(f"MediaMarkt fiyat çekme hatası: {url} -> {e}")
            return None
    
    async def extract_product_details(self, url: str) -> Optional[Dict[str, Any]]:
        """MediaMarkt URL'sinden ürün detaylarını çeker"""
        try:
            await self.page.goto(url, timeout=60000)
            await self.handle_cookie_consent()
            await self.wait_for_page_load()
            
            details = {}
            
            # Ürün adı
            product_name_selectors = [
                'h1[data-test="mms-pdp-product-title"]',
                'h1.product-title',
                '.product-name h1',
                'h1[data-testid="product-name"]'
            ]
            
            for selector in product_name_selectors:
                try:
                    element = self.page.locator(selector).first
                    await element.wait_for(state='visible', timeout=10000)
                    details['product_name'] = await element.inner_text()
                    break
                except PlaywrightTimeoutError:
                    continue
            
            # Marka adı
            brand_selectors = [
                '[data-test="mms-pdp-product-brand"]',
                '.product-brand',
                '.brand-name',
                '[data-testid="product-brand"]'
            ]
            
            for selector in brand_selectors:
                try:
                    element = self.page.locator(selector).first
                    await element.wait_for(state='visible', timeout=10000)
                    details['brand_name'] = await element.inner_text()
                    break
                except PlaywrightTimeoutError:
                    continue
            
            # Fiyat
            details['price'] = await self.extract_price(url)
            
            # Stok durumu
            try:
                add_to_cart = self.page.locator('[data-test="mms-add-to-cart"]').first
                await add_to_cart.wait_for(state='visible', timeout=5000)
                details['availability'] = 'in_stock'
            except:
                details['availability'] = 'out_of_stock'
            
            if details.get('product_name') and details.get('brand_name'):
                logger.info(f"MediaMarkt ürün detayları çekildi: {details['product_name']}")
                return details
            else:
                logger.warning(f"MediaMarkt ürün detayları eksik: {url}")
                return None
                
        except Exception as e:
            logger.error(f"MediaMarkt ürün detayları çekme hatası: {url} -> {e}")
            return None

async def main():
    """MediaMarkt için ana işlem fonksiyonu"""
    async with MediaMarktService() as service:
        await service.process_all_products()

if __name__ == '__main__':
    asyncio.run(main())
