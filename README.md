# Pazar Yeri <PERSON>lleme Sistemi

<PERSON>, `pazar<PERSON>i.db` veritabanındaki ürün tedarikçi linklerinden fiyatları çeker ve veritabanını günceller.

## <PERSON><PERSON><PERSON><PERSON>

- `product_suppliers` tablosundaki URL'lerden otomatik fiyat çekme
- Her tedarikçi için fiyat ve tarih güncelleme
- Ürünler için min/max/ortalama fiyat hesaplama
- Sıfır ve null fiyatları hariç tutma
- Hata yönetimi ve loglama

## Kurulum

1. Python 3.8 veya üzeri sürümün yüklü olduğundan emin olun

2. Gerekli paketleri yükleyin:
```bash
pip install -r requirements.txt
```

## Kullanım

### Ana Programı Çalıştırma
```bash
python price_fetcher.py
```

Bu komut:
- Tüm ürün tedarikçi linklerini işler
- Her link için fiyat <PERSON>
- `product_suppliers` tablosunu günceller
- Her ürün için min/max/ortalama fiyatları hesaplar
- `products` tablosunu günceller

### Test Etme
```bash
python test_price_fetcher.py
```

## Dosya Yapısı

- `price_fetcher.py` - Ana program
- `scrapers.py` - Web scraping fonksiyonları
- `database.py` - Veritabanı işlemleri
- `config.py` - Yapılandırma ayarları
- `test_price_fetcher.py` - Test scripti

## Desteklenen Siteler

- Hepsiburada
- Trendyol
- Teknosa
- MediaMarkt
- Vatan Bilgisayar

## Yapılandırma

`config.py` dosyasında:
- İstek zaman aşımı süreleri
- Tekrar deneme sayısı
- İstekler arası bekleme süresi
- Log seviyesi

## Notlar

- Selenium kullanan siteler için Chrome/Chromium gereklidir
- İlk çalıştırmada ChromeDriver otomatik indirilir
- Rate limiting uygulanır (varsayılan: 1 saniye/istek)