# Fiyat Güncelleme Sistemi

Bu sistem, pazaryeri.db veritabanındaki tedarikçi linklerinden ürün fiyatlarını çeker ve veritabanını günceller.

## <PERSON><PERSON><PERSON>

```bash
pip install -r requirements.txt
```

## Kullanım

### Tüm tedarikçiler için fiyat güncelleme:
```bash
python update_prices.py
```

### Belirli tedarikçiler için fiyat güncelleme:
```bash
python update_prices.py "Hepsiburada" "Trendyol"
```

## Klasör Yapısı

```
PazarYeriServis/
├── scrapers/              # Tüm scraper modülleri
│   ├── __init__.py
│   ├── base_scraper.py    # Temel scraper sınıfı
│   ├── scraper_hepsiburada.py
│   ├── scraper_trendyol.py
│   ├── scraper_teknosa.py
│   └── ...                # Diğer tedarikçi scraperları
├── logs/                  # Log dosyaları
│   └── price_update_*.log
├── update_prices.py       # Ana yönetici script
├── pazaryeri.db          # SQLite veritabanı
├── requirements.txt       # Python bağımlılıkları
└── README_SCRAPERS.md     # Bu dosya
```

## Özellikler

- Her tedarikçi için ayrı Python dosyası
- Sıfır ve null fiyatlar dikkate alınmaz
- Her ürün için min, max ve ortalama fiyat hesaplanır
- Fiyat güncelleme tarihi otomatik güncellenir
- Detaylı loglama sistemi

## Desteklenen Tedarikçiler

- Hepsiburada
- Trendyol
- Teknosa
- MediaMarkt
- Vatan Bilgisayar
- Amazon TR
- N11
- GittiGidiyor
- Çiçeksepeti
- Morhipo
- Genel Tedarikçi

## Notlar

- Bazı e-ticaret siteleri bot koruması kullanmaktadır. 403 Forbidden hatası alırsanız:
  - VPN veya proxy kullanmayı deneyebilirsiniz
  - Selenium veya Playwright gibi tarayıcı otomasyonu araçları kullanılabilir
  - İstekler arasındaki bekleme süresini artırabilirsiniz
  - Site API'si varsa tercih edilmelidir