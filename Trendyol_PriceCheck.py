
"""
Trendyol Supplier Service - Trendyol sitesinden fiyat ve ürün bilgisi çekme servisi
"""

import asyncio
import logging
from typing import Optional, Dict, Any
from playwright.async_api import TimeoutError as PlaywrightTimeoutError
from base_supplier_service import SupplierService

logger = logging.getLogger(__name__)

class TrendyolService(SupplierService):
    """Trendyol için özelleştirilmiş supplier service"""

    def __init__(self):
        super().__init__("Trendyol", "https://www.trendyol.com")

    async def extract_price(self, url: str) -> Optional[float]:
        """Trendyol URL'sinden fiyat çeker"""
        try:
            await self.page.goto(url, timeout=60000)
            await self.handle_cookie_consent()
            await self.wait_for_page_load()

            # Trendyol fiyat seçicileri (öncelik sırasına göre)
            price_selectors = [
                "//div[contains(@class, 'product-price-container')]//span[contains(@class, 'prc-slg-prc')]",
                ".prc-slg-prc",
                ".product-price .prc-slg-prc",
                "[data-test-id='price-current-price']",
                ".price-current",
                ".product-price-container .price",
                ".discounted-price",
                ".current-price"
            ]

            for selector in price_selectors:
                try:
                    price_element = self.page.locator(selector).first
                    await price_element.wait_for(state='visible', timeout=15000)
                    price_text = await price_element.inner_text()

                    if price_text:
                        price = self.clean_price_text(price_text)
                        if price:
                            logger.info(f"Trendyol fiyat bulundu: {price} TL - {url}")
                            return price

                except PlaywrightTimeoutError:
                    continue
                except Exception as e:
                    logger.debug(f"Fiyat seçici hatası ({selector}): {e}")
                    continue

            logger.warning(f"Trendyol fiyat bulunamadı: {url}")
            return None

        except Exception as e:
            logger.error(f"Trendyol fiyat çekme hatası: {url} -> {e}")
            return None

    async def extract_product_details(self, url: str) -> Optional[Dict[str, Any]]:
        """Trendyol URL'sinden ürün detaylarını çeker"""
        try:
            await self.page.goto(url, timeout=60000)
            await self.handle_cookie_consent()
            await self.wait_for_page_load()

            details = {}

            # Ürün adı
            product_name_selectors = [
                'h1[data-test-id="product-name"]',
                'h1.pr-new-br',
                '.product-name h1',
                'h1[role="heading"]'
            ]

            for selector in product_name_selectors:
                try:
                    element = self.page.locator(selector).first
                    await element.wait_for(state='visible', timeout=10000)
                    details['product_name'] = await element.inner_text()
                    break
                except PlaywrightTimeoutError:
                    continue

            # Marka adı
            brand_selectors = [
                'a.product-title-brand-name-anchor',
                '.product-brand-name-with-link',
                '[data-test-id="product-brand"]',
                '.brand-name'
            ]

            for selector in brand_selectors:
                try:
                    element = self.page.locator(selector).first
                    await element.wait_for(state='visible', timeout=10000)
                    details['brand_name'] = await element.inner_text()
                    break
                except PlaywrightTimeoutError:
                    continue

            # Fiyat
            details['price'] = await self.extract_price(url)

            # Stok durumu
            try:
                # Trendyol'da "Sepete Ekle" butonu varsa stokta demektir
                add_to_cart = self.page.locator('[data-test-id="add-to-cart"]').first
                await add_to_cart.wait_for(state='visible', timeout=5000)
                details['availability'] = 'in_stock'
            except:
                details['availability'] = 'out_of_stock'

            if details.get('product_name') and details.get('brand_name'):
                logger.info(f"Trendyol ürün detayları çekildi: {details['product_name']}")
                return details
            else:
                logger.warning(f"Trendyol ürün detayları eksik: {url}")
                return None

        except Exception as e:
            logger.error(f"Trendyol ürün detayları çekme hatası: {url} -> {e}")
            return None

    async def handle_cookie_consent(self):
        """Trendyol özel çerez onayı"""
        try:
            # Trendyol çerez onayı butonları
            cookie_selectors = [
                'button#onetrust-accept-btn-handler',
                'button[data-testid="cookie-accept"]',
                'button:has-text("Kabul Et")',
                'button:has-text("Tamam")',
                '.cookie-accept-button',
                '#cookieUsageAcceptButton'
            ]

            for selector in cookie_selectors:
                try:
                    await self.page.locator(selector).click(timeout=5000)
                    logger.info(f"Trendyol çerez onayı tıklandı: {selector}")
                    break
                except PlaywrightTimeoutError:
                    continue

        except Exception as e:
            logger.debug(f"Trendyol çerez onayı handle edilemedi: {e}")

async def main():
    """Trendyol için ana işlem fonksiyonu"""
    async with TrendyolService() as service:
        await service.process_all_products()

if __name__ == '__main__':
    asyncio.run(main())
