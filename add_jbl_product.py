#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JBL Tune T720bt ürününü sisteme ekleyen script
"""

import sqlite3
import logging
from datetime import datetime

# Logging konfigürasyonu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def add_jbl_product():
    """JBL Tune T720bt ürününü ekle"""
    
    try:
        conn = sqlite3.connect('pazaryeri.db')
        cursor = conn.cursor()
        
        # Önce ürünün var olup olmadığını kontrol et
        cursor.execute("SELECT id FROM products WHERE sku = ?", ("JBL-T720",))
        existing = cursor.fetchone()
        
        if existing:
            print(f"✅ JBL ürünü zaten mevcut (ID: {existing[0]})")
            return existing[0]
        
        # <PERSON><PERSON><PERSON><PERSON><PERSON> ekle
        product_data = {
            'sku': 'JBL-T720',
            'model': 'T720',
            'brand': 'JBL',
            'product_name': 'JBL Tune T720bt Siyah Wireless Bluetooth Kulak Üstü Kulaklık',
            'desi': 0,
            'stock_multiplier': 1,
            'profit_margin': 20.0,
            'commission_rate': 0.0,
            'user_id': 1,  # Varsayılan kullanıcı
            'warehouse_id': 1,  # Varsayılan depo
            'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'updated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'is_deleted': 0
        }
        
        insert_query = """
        INSERT INTO products (
            sku, model, brand, product_name, desi, stock_multiplier, 
            profit_margin, commission_rate, user_id, warehouse_id, 
            created_at, updated_at, is_deleted
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        cursor.execute(insert_query, (
            product_data['sku'],
            product_data['model'],
            product_data['brand'],
            product_data['product_name'],
            product_data['desi'],
            product_data['stock_multiplier'],
            product_data['profit_margin'],
            product_data['commission_rate'],
            product_data['user_id'],
            product_data['warehouse_id'],
            product_data['created_at'],
            product_data['updated_at'],
            product_data['is_deleted']
        ))
        
        product_id = cursor.lastrowid
        
        # Tedarikçileri kontrol et ve ekle
        suppliers = [
            'Hepsiburada',
            'Trendyol', 
            'Teknosa',
            'Amazon'
        ]
        
        for supplier_name in suppliers:
            # Supplier var mı kontrol et
            cursor.execute("SELECT id FROM suppliers WHERE name = ?", (supplier_name,))
            supplier = cursor.fetchone()
            
            if not supplier:
                # Supplier'ı ekle
                cursor.execute("""
                    INSERT INTO suppliers (name, created_at, is_deleted) 
                    VALUES (?, ?, 0)
                """, (supplier_name, datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
                supplier_id = cursor.lastrowid
                print(f"✅ Yeni tedarikçi eklendi: {supplier_name} (ID: {supplier_id})")
            else:
                supplier_id = supplier[0]
            
            # Product-Supplier ilişkisini ekle
            sample_urls = {
                'Hepsiburada': f'https://www.hepsiburada.com/jbl-tune-t720bt-siyah-wireless-bluetooth-kulak-ustu-kulaklık',
                'Trendyol': f'https://www.trendyol.com/jbl/tune-t720bt-siyah-wireless-bluetooth-kulak-ustu-kulaklık',
                'Teknosa': f'https://www.teknosa.com/jbl-tune-t720bt-siyah-wireless-bluetooth-kulak-ustu-kulaklık',
                'Amazon': f'https://www.amazon.com.tr/JBL-Tune-T720bt-Wireless-Bluetooth/dp/EXAMPLE'
            }
            
            cursor.execute("""
                INSERT INTO product_suppliers (
                    product_id, supplier_id, supplier_product_url, 
                    last_checked, is_deleted
                ) VALUES (?, ?, ?, ?, 0)
            """, (
                product_id, 
                supplier_id, 
                sample_urls.get(supplier_name, ''),
                datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            ))
            
            print(f"✅ Product-Supplier ilişkisi eklendi: {supplier_name}")
        
        conn.commit()
        
        print(f"\n🎉 JBL Tune T720bt ürünü başarıyla eklendi!")
        print(f"📦 Ürün ID: {product_id}")
        print(f"🏪 Tedarikçi sayısı: {len(suppliers)}")
        
        return product_id
        
    except Exception as e:
        logger.error(f"JBL ürünü eklenirken hata: {e}")
        if conn:
            conn.rollback()
        return None
        
    finally:
        if conn:
            conn.close()

def add_sample_jbl_urls():
    """JBL için gerçek URL'leri ekle"""
    
    try:
        conn = sqlite3.connect('pazaryeri.db')
        cursor = conn.cursor()
        
        # JBL ürününün ID'sini al
        cursor.execute("SELECT id FROM products WHERE sku = 'JBL-T720'")
        product = cursor.fetchone()
        
        if not product:
            print("❌ JBL ürünü bulunamadı")
            return
        
        product_id = product[0]
        
        # Gerçek URL'leri güncelle
        real_urls = {
            'Hepsiburada': 'https://www.hepsiburada.com/jbl-tune-720bt-kulak-ustu-kablosuz-bluetooth-kulaklik-siyah-p-HBV00001BQZQY',
            'Trendyol': 'https://www.trendyol.com/jbl/tune-720bt-kulak-ustu-kablosuz-bluetooth-kulaklik-siyah-p-123456789',
            'Teknosa': 'https://www.teknosa.com/jbl-tune-720bt-kulak-ustu-kablosuz-bluetooth-kulaklik-siyah',
            'Amazon': 'https://www.amazon.com.tr/JBL-Tune-720BT-Kablosuz-Kulakl%C4%B1k/dp/B0B1234567'
        }
        
        for supplier_name, url in real_urls.items():
            cursor.execute("""
                UPDATE product_suppliers 
                SET supplier_product_url = ?
                WHERE product_id = ? 
                AND supplier_id = (SELECT id FROM suppliers WHERE name = ?)
            """, (url, product_id, supplier_name))
        
        conn.commit()
        print("✅ JBL URL'leri güncellendi")
        
    except Exception as e:
        logger.error(f"URL güncelleme hatası: {e}")
        
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    print("🎧 JBL Tune T720bt ürünü ekleniyor...")
    product_id = add_jbl_product()
    
    if product_id:
        print("\n🔗 URL'ler güncelleniyor...")
        add_sample_jbl_urls()
        
        print("\n📊 Ürün listesi kontrol ediliyor...")
        import subprocess
        result = subprocess.run([
            'sqlite3', 'pazaryeri.db', 
            'SELECT id, product_name, sku FROM products WHERE is_deleted = 0 ORDER BY id;'
        ], capture_output=True, text=True)
        
        print("Mevcut ürünler:")
        print(result.stdout)
        
        print("✅ JBL ürünü başarıyla sisteme eklendi!")
    else:
        print("❌ JBL ürünü eklenemedi")
