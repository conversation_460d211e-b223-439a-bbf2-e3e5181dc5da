"""
Base Supplier Service - Tüm supplier servisleri için ortak base class
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, List
from datetime import datetime
from playwright.async_api import async_playwright, TimeoutError as PlaywrightTimeoutError
import database_operations as db

# Logging konfigürasyonu
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SupplierService(ABC):
    """
    Tüm supplier servisleri için base class.
    Her supplier için özelleştirilmiş scraping mantığı implement edilmelidir.
    """
    
    def __init__(self, supplier_name: str, base_url: str):
        self.supplier_name = supplier_name
        self.base_url = base_url
        self.browser = None
        self.page = None
        
    async def __aenter__(self):
        """Async context manager - browser başlatma"""
        self.playwright = await async_playwright().__aenter__()
        self.browser = await self.playwright.chromium.launch(headless=True)
        self.page = await self.browser.new_page()
        
        # Genel browser ayarları
        await self.page.set_viewport_size({"width": 1920, "height": 1080})
        await self.page.set_extra_http_headers({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager - browser kapatma"""
        if self.browser:
            await self.browser.close()
        if hasattr(self, 'playwright'):
            await self.playwright.__aexit__(exc_type, exc_val, exc_tb)
    
    @abstractmethod
    async def extract_price(self, url: str) -> Optional[float]:
        """
        Verilen URL'den fiyat çeker.
        Her supplier için özelleştirilmelidir.
        """
        pass
    
    @abstractmethod
    async def extract_product_details(self, url: str) -> Optional[Dict[str, Any]]:
        """
        Verilen URL'den ürün detaylarını çeker.
        Her supplier için özelleştirilmelidir.
        
        Returns:
            Dict containing: product_name, brand_name, price, availability, etc.
        """
        pass
    
    async def handle_cookie_consent(self):
        """
        Çerez onayı popup'larını handle eder.
        Genel implementasyon, gerekirse override edilebilir.
        """
        try:
            # Yaygın çerez onayı butonları
            cookie_selectors = [
                'button#onetrust-accept-btn-handler',
                'button[data-testid="cookie-accept"]',
                'button:has-text("Kabul Et")',
                'button:has-text("Accept")',
                'button:has-text("Tamam")',
                '.cookie-accept',
                '#cookie-accept'
            ]
            
            for selector in cookie_selectors:
                try:
                    await self.page.locator(selector).click(timeout=3000)
                    logger.info(f"Çerez onayı tıklandı: {selector}")
                    break
                except PlaywrightTimeoutError:
                    continue
                    
        except Exception as e:
            logger.debug(f"Çerez onayı handle edilemedi: {e}")
    
    async def wait_for_page_load(self, timeout: int = 30000):
        """
        Sayfanın tamamen yüklenmesini bekler.
        """
        try:
            await self.page.wait_for_load_state('networkidle', timeout=timeout)
        except PlaywrightTimeoutError:
            logger.warning("Sayfa yüklenme timeout'u")
    
    async def scroll_page(self, scroll_count: int = 3):
        """
        Sayfayı aşağı kaydırır - lazy loading için.
        """
        for i in range(scroll_count):
            await self.page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            await asyncio.sleep(1)
    
    def clean_price_text(self, price_text: str) -> Optional[float]:
        """
        Fiyat metnini temizler ve float'a çevirir.
        """
        if not price_text:
            return None
            
        try:
            # Yaygın fiyat formatlarını temizle
            cleaned = price_text.strip()
            
            # Para birimi sembollerini kaldır
            for symbol in ['TL', '₺', '$', '€', '£', 'USD', 'EUR', 'GBP']:
                cleaned = cleaned.replace(symbol, '')
            
            # Nokta ve virgül işlemlerini düzenle
            # Türkçe format: 1.234,56 -> 1234.56
            if ',' in cleaned and '.' in cleaned:
                # Hem nokta hem virgül varsa, son virgülü decimal ayırıcı olarak kabul et
                parts = cleaned.split(',')
                if len(parts) == 2 and len(parts[1]) <= 2:
                    cleaned = parts[0].replace('.', '') + '.' + parts[1]
                else:
                    cleaned = cleaned.replace(',', '')
            elif ',' in cleaned:
                # Sadece virgül varsa, decimal ayırıcı olarak kabul et
                parts = cleaned.split(',')
                if len(parts) == 2 and len(parts[1]) <= 2:
                    cleaned = parts[0] + '.' + parts[1]
                else:
                    cleaned = cleaned.replace(',', '')
            
            # Sadece rakam, nokta ve boşluk bırak
            cleaned = ''.join(c for c in cleaned if c.isdigit() or c == '.')
            
            return float(cleaned) if cleaned else None
            
        except (ValueError, AttributeError) as e:
            logger.error(f"Fiyat temizleme hatası: {price_text} -> {e}")
            return None
    
    async def update_product_price(self, product_supplier_id: int, new_price: Optional[float]):
        """
        Veritabanında ürün fiyatını günceller.
        """
        if new_price is not None:
            db.update_price(product_supplier_id, new_price)
            logger.info(f"Fiyat güncellendi - ID: {product_supplier_id}, Fiyat: {new_price}")
        else:
            logger.warning(f"Fiyat güncellenemedi - ID: {product_supplier_id}")
    
    async def process_all_products(self):
        """
        Bu supplier'a ait tüm ürünleri işler.
        """
        logger.info(f"{self.supplier_name} için fiyat güncelleme başlatıldı")
        
        products = db.get_products_for_supplier(self.supplier_name)
        
        if not products:
            logger.info(f"{self.supplier_name} için ürün bulunamadı")
            return
        
        success_count = 0
        error_count = 0
        
        for product in products:
            try:
                logger.info(f"İşleniyor: Ürün ID {product['product_id']} - URL: {product['supplier_product_url']}")
                
                price = await self.extract_price(product['supplier_product_url'])
                await self.update_product_price(product['product_supplier_id'], price)
                
                if price is not None:
                    success_count += 1
                else:
                    error_count += 1
                    
                # Rate limiting - siteler arası bekleme
                await asyncio.sleep(2)
                
            except Exception as e:
                logger.error(f"Ürün işleme hatası - ID: {product['product_id']}, Hata: {e}")
                error_count += 1
        
        logger.info(f"{self.supplier_name} işlemi tamamlandı. Başarılı: {success_count}, Hatalı: {error_count}")
    
    async def add_new_product(self, product_url: str) -> Optional[Dict[str, Any]]:
        """
        Yeni ürün ekler ve detaylarını çeker.
        """
        try:
            details = await self.extract_product_details(product_url)
            
            if details and details.get('product_name') and details.get('brand_name'):
                product_id, supplier_id, product_supplier_id = db.add_or_update_product_and_supplier_link(
                    details['product_name'],
                    details['brand_name'],
                    self.supplier_name,
                    product_url
                )
                
                logger.info(f"Yeni ürün eklendi - Ürün ID: {product_id}, Supplier ID: {supplier_id}")
                return {
                    'product_id': product_id,
                    'supplier_id': supplier_id,
                    'product_supplier_id': product_supplier_id,
                    'details': details
                }
            else:
                logger.error(f"Ürün detayları eksik: {product_url}")
                return None
                
        except Exception as e:
            logger.error(f"Yeni ürün ekleme hatası: {product_url} -> {e}")
            return None
