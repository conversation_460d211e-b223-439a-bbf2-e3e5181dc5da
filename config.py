import os

# Database Configuration
DATABASE_PATH = os.path.join(os.path.dirname(__file__), 'pazaryeri.db')

# Request Configuration
REQUEST_TIMEOUT = 30  # seconds
MAX_RETRIES = 3
RETRY_DELAY = 2  # seconds between retries

# User Agent for web requests
USER_AGENT = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'

# Rate Limiting
DELAY_BETWEEN_REQUESTS = 1  # seconds

# Logging Configuration
LOG_LEVEL = 'INFO'
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

# Price extraction selectors for different sites
PRICE_SELECTORS = {
    'hepsiburada.com': [
        {'selector': 'span[data-bind*="markupText:\'currentPriceBeforePoint\'"]', 'type': 'css'},
        {'selector': 'span.price-value', 'type': 'css'},
        {'selector': '//span[@class="product-price"]//text()', 'type': 'xpath'}
    ],
    'trendyol.com': [
        {'selector': 'span.prc-dsc', 'type': 'css'},
        {'selector': 'span.prc-slg', 'type': 'css'},
        {'selector': '//div[@class="pr-bx-w"]//span[@class="prc-slg"]/text()', 'type': 'xpath'}
    ],
    'teknosa.com': [
        {'selector': 'span.prd-prc', 'type': 'css'},
        {'selector': 'div.product-price span', 'type': 'css'}
    ],
    'mediamarkt.com.tr': [
        {'selector': 'div[data-test="price-now"]', 'type': 'css'},
        {'selector': 'span.price', 'type': 'css'}
    ],
    'vatanbilgisayar.com': [
        {'selector': 'span.product__price', 'type': 'css'},
        {'selector': 'div.product-list__cost span', 'type': 'css'}
    ]
}

# Sites that require JavaScript rendering
JS_REQUIRED_SITES = []  # Temporarily disabled for testing