#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fiyat geçmişi tablosu oluşturan script
"""

import sqlite3
import logging
from datetime import datetime

# Logging konfigürasyonu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_price_history_table():
    """Fiyat geçmişi tablosunu oluştur"""
    
    try:
        conn = sqlite3.connect('pazaryeri.db')
        cursor = conn.cursor()
        
        # Fiyat geçmişi tablosu
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS price_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_supplier_id INTEGER NOT NULL,
                price REAL NOT NULL,
                old_price REAL,
                price_change REAL,
                price_change_percent REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (product_supplier_id) REFERENCES product_suppliers(id)
            )
        ''')
        
        # Fiyat geçmişi için indeksler
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_price_history_ps_id 
            ON price_history(product_supplier_id)
        ''')
        
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_price_history_date 
            ON price_history(created_at)
        ''')
        
        # Fiyat istatistikleri tablosu (günlük/haftalık/aylık özetler için)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS price_statistics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER NOT NULL,
                supplier_id INTEGER,
                period_type TEXT NOT NULL, -- 'daily', 'weekly', 'monthly'
                period_date DATE NOT NULL,
                min_price REAL,
                max_price REAL,
                avg_price REAL,
                price_count INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (product_id) REFERENCES products(id),
                FOREIGN KEY (supplier_id) REFERENCES suppliers(id)
            )
        ''')
        
        # Fiyat istatistikleri için indeksler
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_price_stats_product 
            ON price_statistics(product_id, period_type, period_date)
        ''')
        
        conn.commit()
        logger.info("Fiyat geçmişi tabloları başarıyla oluşturuldu")
        
        # Mevcut tabloları kontrol et
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        logger.info(f"Mevcut tablolar: {[table[0] for table in tables]}")
        
        conn.close()
        
    except Exception as e:
        logger.error(f"Tablo oluşturulurken hata: {e}")

def add_sample_price_history():
    """Örnek fiyat geçmişi verisi ekle"""
    
    try:
        conn = sqlite3.connect('pazaryeri.db')
        cursor = conn.cursor()
        
        # Mevcut product_suppliers'ları al
        cursor.execute("""
            SELECT ps.id, ps.supplier_price 
            FROM product_suppliers ps 
            WHERE ps.supplier_price IS NOT NULL 
            AND ps.supplier_price > 0
            LIMIT 5
        """)
        
        suppliers = cursor.fetchall()
        
        for ps_id, current_price in suppliers:
            # Geçmiş fiyat verisi simüle et
            import random
            
            # Son 30 gün için rastgele fiyat değişimleri
            for days_ago in range(30, 0, -1):
                # Fiyatta %5-15 arası değişim
                change_percent = random.uniform(-15, 15)
                old_price = current_price * (1 + change_percent / 100)
                
                # Tarih hesapla
                from datetime import datetime, timedelta
                date = datetime.now() - timedelta(days=days_ago)
                
                cursor.execute("""
                    INSERT INTO price_history 
                    (product_supplier_id, price, old_price, price_change, price_change_percent, created_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    ps_id, 
                    old_price, 
                    None,  # İlk kayıt için old_price yok
                    0,     # İlk kayıt için değişim yok
                    0,     # İlk kayıt için değişim yüzdesi yok
                    date.strftime('%Y-%m-%d %H:%M:%S')
                ))
        
        conn.commit()
        logger.info("Örnek fiyat geçmişi verileri eklendi")
        conn.close()
        
    except Exception as e:
        logger.error(f"Örnek veri eklenirken hata: {e}")

if __name__ == "__main__":
    print("🗄️ Fiyat geçmişi tabloları oluşturuluyor...")
    create_price_history_table()
    
    print("📊 Örnek fiyat geçmişi verileri ekleniyor...")
    add_sample_price_history()
    
    print("✅ Fiyat geçmişi sistemi hazır!")
