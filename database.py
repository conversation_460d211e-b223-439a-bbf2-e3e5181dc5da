import sqlite3
import logging
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from config import DATABASE_PATH

logger = logging.getLogger(__name__)


class Database:
    def __init__(self):
        self.db_path = DATABASE_PATH
        
    def get_connection(self):
        """Create and return a database connection"""
        return sqlite3.connect(self.db_path)
    
    def get_all_product_suppliers(self) -> List[Dict]:
        """Get all product suppliers with their URLs"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        query = """
        SELECT 
            ps.id,
            ps.product_id,
            ps.supplier_product_url,
            ps.supplier_price,
            p.sku,
            p.product_name
        FROM product_suppliers ps
        JOIN products p ON ps.product_id = p.id
        WHERE ps.is_deleted = 0 
        AND ps.supplier_product_url IS NOT NULL
        ORDER BY ps.product_id
        """
        
        cursor.execute(query)
        columns = [desc[0] for desc in cursor.description]
        results = []
        
        for row in cursor.fetchall():
            results.append(dict(zip(columns, row)))
        
        conn.close()
        return results
    
    def update_supplier_price(self, supplier_id: int, price: Optional[float]) -> bool:
        """Update supplier price and last_checked timestamp"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            if price is not None and price > 0:
                query = """
                UPDATE product_suppliers 
                SET supplier_price = ?, last_checked = ?
                WHERE id = ?
                """
                cursor.execute(query, (price, current_time, supplier_id))
            else:
                # Only update last_checked if price is invalid
                query = """
                UPDATE product_suppliers 
                SET last_checked = ?
                WHERE id = ?
                """
                cursor.execute(query, (current_time, supplier_id))
            
            conn.commit()
            logger.info(f"Updated supplier {supplier_id} with price: {price}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating supplier {supplier_id}: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()
    
    def get_product_prices(self, product_id: int) -> List[float]:
        """Get all valid prices for a product (excluding null and zero)"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        query = """
        SELECT supplier_price 
        FROM product_suppliers 
        WHERE product_id = ? 
        AND is_deleted = 0 
        AND supplier_price IS NOT NULL 
        AND supplier_price > 0
        """
        
        cursor.execute(query, (product_id,))
        prices = [row[0] for row in cursor.fetchall()]
        
        conn.close()
        return prices
    
    def update_product_prices(self, product_id: int, min_price: float, 
                            max_price: float, avg_price: float) -> bool:
        """Update min, max, and average prices for a product"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            query = """
            UPDATE products 
            SET min_price = ?, 
                max_price = ?, 
                avg_price = ?, 
                price_updated_at = ?
            WHERE id = ?
            """
            
            cursor.execute(query, (min_price, max_price, avg_price, current_time, product_id))
            conn.commit()
            
            logger.info(f"Updated product {product_id} prices - Min: {min_price}, Max: {max_price}, Avg: {avg_price}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating product {product_id} prices: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()
    
    def get_all_products(self) -> List[int]:
        """Get all product IDs"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        query = "SELECT DISTINCT id FROM products WHERE is_deleted = 0"
        cursor.execute(query)
        
        product_ids = [row[0] for row in cursor.fetchall()]
        conn.close()
        
        return product_ids