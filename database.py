import sqlite3
import logging
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from config import DATABASE_PATH

logger = logging.getLogger(__name__)


class Database:
    def __init__(self):
        self.db_path = DATABASE_PATH
        
    def get_connection(self):
        """Create and return a database connection"""
        return sqlite3.connect(self.db_path)
    
    def get_all_product_suppliers(self) -> List[Dict]:
        """Get all product suppliers with their URLs"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        query = """
        SELECT 
            ps.id,
            ps.product_id,
            ps.supplier_product_url,
            ps.supplier_price,
            p.sku,
            p.product_name
        FROM product_suppliers ps
        JOIN products p ON ps.product_id = p.id
        WHERE ps.is_deleted = 0 
        AND ps.supplier_product_url IS NOT NULL
        ORDER BY ps.product_id
        """
        
        cursor.execute(query)
        columns = [desc[0] for desc in cursor.description]
        results = []
        
        for row in cursor.fetchall():
            results.append(dict(zip(columns, row)))
        
        conn.close()
        return results
    
    def update_supplier_price(self, supplier_id: int, price: Optional[float]) -> bool:
        """Update supplier price and last_checked timestamp with price history tracking"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # Önce mevcut fiyatı al
            cursor.execute("SELECT supplier_price FROM product_suppliers WHERE id = ?", (supplier_id,))
            result = cursor.fetchone()
            old_price = result[0] if result and result[0] else None

            if price is not None and price > 0:
                # Fiyat güncelle
                query = """
                UPDATE product_suppliers
                SET supplier_price = ?, last_checked = ?
                WHERE id = ?
                """
                cursor.execute(query, (price, current_time, supplier_id))

                # Fiyat geçmişine kaydet (sadece fiyat değişmişse)
                if old_price is None or abs(old_price - price) > 0.01:  # 1 kuruş farktan büyükse
                    price_change = price - old_price if old_price else 0
                    price_change_percent = (price_change / old_price * 100) if old_price and old_price > 0 else 0

                    cursor.execute("""
                        INSERT INTO price_history
                        (product_supplier_id, price, old_price, price_change, price_change_percent)
                        VALUES (?, ?, ?, ?, ?)
                    """, (supplier_id, price, old_price, price_change, price_change_percent))

                    logger.info(f"Price history recorded for supplier {supplier_id}: {old_price} -> {price} ({price_change_percent:.2f}%)")
            else:
                # Only update last_checked if price is invalid
                query = """
                UPDATE product_suppliers
                SET last_checked = ?
                WHERE id = ?
                """
                cursor.execute(query, (current_time, supplier_id))

            conn.commit()
            logger.info(f"Updated supplier {supplier_id} with price: {price}")
            return True

        except Exception as e:
            logger.error(f"Error updating supplier {supplier_id}: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()
    
    def get_product_prices(self, product_id: int) -> List[float]:
        """Get all valid prices for a product (excluding null and zero)"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        query = """
        SELECT supplier_price 
        FROM product_suppliers 
        WHERE product_id = ? 
        AND is_deleted = 0 
        AND supplier_price IS NOT NULL 
        AND supplier_price > 0
        """
        
        cursor.execute(query, (product_id,))
        prices = [row[0] for row in cursor.fetchall()]
        
        conn.close()
        return prices
    
    def update_product_prices(self, product_id: int, min_price: float, 
                            max_price: float, avg_price: float) -> bool:
        """Update min, max, and average prices for a product"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            query = """
            UPDATE products 
            SET min_price = ?, 
                max_price = ?, 
                avg_price = ?, 
                price_updated_at = ?
            WHERE id = ?
            """
            
            cursor.execute(query, (min_price, max_price, avg_price, current_time, product_id))
            conn.commit()
            
            logger.info(f"Updated product {product_id} prices - Min: {min_price}, Max: {max_price}, Avg: {avg_price}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating product {product_id} prices: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()
    
    def get_all_products(self) -> List[int]:
        """Get all product IDs"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        query = "SELECT DISTINCT id FROM products WHERE is_deleted = 0"
        cursor.execute(query)
        
        product_ids = [row[0] for row in cursor.fetchall()]
        conn.close()
        
        return product_ids

    def get_price_history(self, product_supplier_id: int, days: int = 30) -> List[Dict]:
        """Get price history for a product supplier"""
        conn = self.get_connection()
        cursor = conn.cursor()

        query = """
        SELECT
            ph.price,
            ph.old_price,
            ph.price_change,
            ph.price_change_percent,
            ph.created_at,
            p.product_name,
            s.name as supplier_name
        FROM price_history ph
        JOIN product_suppliers ps ON ph.product_supplier_id = ps.id
        JOIN products p ON ps.product_id = p.id
        JOIN suppliers s ON ps.supplier_id = s.id
        WHERE ph.product_supplier_id = ?
        AND ph.created_at >= datetime('now', '-{} days')
        ORDER BY ph.created_at DESC
        """.format(days)

        cursor.execute(query, (product_supplier_id,))
        columns = [desc[0] for desc in cursor.description]
        results = []

        for row in cursor.fetchall():
            results.append(dict(zip(columns, row)))

        conn.close()
        return results

    def get_product_price_trends(self, product_id: int, days: int = 30) -> Dict:
        """Get price trends for all suppliers of a product"""
        conn = self.get_connection()
        cursor = conn.cursor()

        query = """
        SELECT
            s.name as supplier_name,
            ps.id as product_supplier_id,
            ps.supplier_price as current_price,
            COUNT(ph.id) as price_changes,
            MIN(ph.price) as min_price_period,
            MAX(ph.price) as max_price_period,
            AVG(ph.price) as avg_price_period,
            (ps.supplier_price - LAG(ph.price) OVER (PARTITION BY ps.id ORDER BY ph.created_at DESC)) as latest_change
        FROM product_suppliers ps
        JOIN suppliers s ON ps.supplier_id = s.id
        LEFT JOIN price_history ph ON ps.id = ph.product_supplier_id
            AND ph.created_at >= datetime('now', '-{} days')
        WHERE ps.product_id = ? AND ps.is_deleted = 0
        GROUP BY ps.id, s.name, ps.supplier_price
        ORDER BY ps.supplier_price ASC
        """.format(days)

        cursor.execute(query, (product_id,))
        columns = [desc[0] for desc in cursor.description]
        results = []

        for row in cursor.fetchall():
            results.append(dict(zip(columns, row)))

        conn.close()
        return {
            'product_id': product_id,
            'period_days': days,
            'suppliers': results
        }

    def get_price_alerts(self, threshold_percent: float = 10.0) -> List[Dict]:
        """Get recent significant price changes"""
        conn = self.get_connection()
        cursor = conn.cursor()

        query = """
        SELECT
            ph.product_supplier_id,
            ph.price,
            ph.old_price,
            ph.price_change,
            ph.price_change_percent,
            ph.created_at,
            p.product_name,
            p.sku,
            s.name as supplier_name
        FROM price_history ph
        JOIN product_suppliers ps ON ph.product_supplier_id = ps.id
        JOIN products p ON ps.product_id = p.id
        JOIN suppliers s ON ps.supplier_id = s.id
        WHERE ABS(ph.price_change_percent) >= ?
        AND ph.created_at >= datetime('now', '-7 days')
        ORDER BY ABS(ph.price_change_percent) DESC, ph.created_at DESC
        LIMIT 50
        """

        cursor.execute(query, (threshold_percent,))
        columns = [desc[0] for desc in cursor.description]
        results = []

        for row in cursor.fetchall():
            results.append(dict(zip(columns, row)))

        conn.close()
        return results