#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gerçek JBL Tune 720BT URL'lerini bulan script
"""

import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def find_jbl_urls():
    """JBL Tune 720BT için gerçek URL'leri bul"""
    
    chrome_options = Options()
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument('--start-maximized')
    
    browser = None
    found_urls = {}
    
    try:
        browser = webdriver.Chrome(options=chrome_options)
        browser.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        search_sites = [
            {
                'name': 'Hepsiburada',
                'search_url': 'https://www.hepsiburada.com/ara?q=jbl+tune+720bt',
                'result_selector': '.product-item a'
            },
            {
                'name': 'Trendyol', 
                'search_url': 'https://www.trendyol.com/sr?q=jbl+tune+720bt',
                'result_selector': '.p-card-wrppr a'
            },
            {
                'name': 'Teknosa',
                'search_url': 'https://www.teknosa.com/arama?q=jbl+tune+720bt',
                'result_selector': '.product-item a'
            }
        ]
        
        print("🔍 JBL Tune 720BT için gerçek URL'ler aranıyor...\n")
        
        for site in search_sites:
            print(f"🔍 {site['name']} aranıyor...")
            
            try:
                browser.get(site['search_url'])
                time.sleep(5)
                
                # İlk ürün linkini bul
                try:
                    first_product = browser.find_element(By.CSS_SELECTOR, site['result_selector'])
                    product_url = first_product.get_attribute('href')
                    
                    if product_url and 'jbl' in product_url.lower():
                        found_urls[site['name']] = product_url
                        print(f"✅ {site['name']}: {product_url}")
                    else:
                        print(f"❌ {site['name']}: Uygun ürün bulunamadı")
                        
                except Exception as e:
                    print(f"❌ {site['name']}: Element bulunamadı - {e}")
                    
            except Exception as e:
                print(f"❌ {site['name']}: Sayfa yüklenemedi - {e}")
            
            time.sleep(2)
        
        # Amazon için manuel arama
        print(f"\n🔍 Amazon aranıyor...")
        try:
            browser.get('https://www.amazon.com.tr/s?k=jbl+tune+720bt')
            time.sleep(5)
            
            # İlk ürün linkini bul
            try:
                first_product = browser.find_element(By.CSS_SELECTOR, '[data-component-type="s-search-result"] h2 a')
                product_url = first_product.get_attribute('href')
                
                if product_url:
                    # Amazon URL'ini temizle
                    if '?' in product_url:
                        product_url = product_url.split('?')[0]
                    found_urls['Amazon'] = f"https://www.amazon.com.tr{product_url}" if product_url.startswith('/') else product_url
                    print(f"✅ Amazon: {found_urls['Amazon']}")
                else:
                    print(f"❌ Amazon: Uygun ürün bulunamadı")
                    
            except Exception as e:
                print(f"❌ Amazon: Element bulunamadı - {e}")
                
        except Exception as e:
            print(f"❌ Amazon: Sayfa yüklenemedi - {e}")
        
        return found_urls
        
    except Exception as e:
        print(f"❌ Browser hatası: {e}")
        return {}
        
    finally:
        if browser:
            browser.quit()

def update_jbl_urls(urls):
    """Bulunan URL'leri veritabanında güncelle"""
    
    if not urls:
        print("❌ Güncellenecek URL bulunamadı")
        return
    
    try:
        import sqlite3
        conn = sqlite3.connect('pazaryeri.db')
        cursor = conn.cursor()
        
        print(f"\n📝 Veritabanında URL'ler güncelleniyor...")
        
        for supplier_name, url in urls.items():
            cursor.execute("""
                UPDATE product_suppliers 
                SET supplier_product_url = ?
                WHERE product_id = (SELECT id FROM products WHERE sku = 'JBL-T720')
                AND supplier_id = (SELECT id FROM suppliers WHERE name = ?)
            """, (url, supplier_name))
            
            if cursor.rowcount > 0:
                print(f"✅ {supplier_name}: URL güncellendi")
            else:
                print(f"❌ {supplier_name}: Güncelleme başarısız")
        
        conn.commit()
        conn.close()
        
        print(f"\n🎉 {len(urls)} URL başarıyla güncellendi!")
        
    except Exception as e:
        print(f"❌ Veritabanı güncelleme hatası: {e}")

if __name__ == "__main__":
    print("🎧 JBL Tune 720BT gerçek URL'leri bulunuyor...")
    
    urls = find_jbl_urls()
    
    if urls:
        print(f"\n📋 Bulunan URL'ler:")
        for site, url in urls.items():
            print(f"  • {site}: {url}")
        
        update_jbl_urls(urls)
    else:
        print("\n❌ Hiç URL bulunamadı")
    
    print("\n✅ İşlem tamamlandı!")
