#!/usr/bin/env python3
"""
Interactive Price Scraper
Browser açarak manuel müdahale imkanı sağlar ve fiyatları otomatik yakalar
"""
import sqlite3
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from bs4 import BeautifulSoup
import time
import re
from datetime import datetime
from typing import Optional, Dict, Tuple
import logging

# Logging setup
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class InteractivePriceScraper:
    def __init__(self, db_path: str = "pazaryeri.db"):
        self.db_path = db_path
        self.driver = None
        self.setup_driver()
        
    def setup_driver(self):
        """Chrome driver'ı başlat"""
        chrome_options = Options()
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        chrome_options.add_argument('--start-maximized')
        
        self.driver = webdriver.Chrome(options=chrome_options)
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        logger.info("Browser başlatıldı")
        
    def get_db_connection(self):
        return sqlite3.connect(self.db_path)
        
    def get_product_suppliers(self) -> list:
        """Tüm product_supplier kayıtlarını getir"""
        conn = self.get_db_connection()
        cursor = conn.cursor()
        cursor.execute("""
            SELECT ps.id, ps.product_id, ps.supplier_product_url, s.name as supplier_name
            FROM product_suppliers ps
            JOIN suppliers s ON ps.supplier_id = s.id
            WHERE ps.is_deleted = 0
            ORDER BY ps.product_id, s.name
        """)
        results = cursor.fetchall()
        conn.close()
        return results
        
    def clean_price(self, price_text: str) -> Optional[float]:
        """Fiyat metnini temizle ve float'a çevir"""
        try:
            # TL, ₺ ve diğer karakterleri temizle
            price_text = re.sub(r'[^\d,.]', '', price_text)
            # Türkçe format: nokta binlik ayracı, virgül ondalık
            price_text = price_text.replace('.', '').replace(',', '.')
            return float(price_text)
        except:
            return None
            
    def extract_price_hepsiburada(self, html: str) -> Optional[float]:
        """Hepsiburada fiyat çıkarma"""
        soup = BeautifulSoup(html, 'html.parser')
        
        # Hepsiburada selectors
        selectors = [
            ('span', {'data-bind': re.compile(r'currentPriceBeforePoint')}),
            ('span', {'class': 'price-value'}),
            ('span', {'class': re.compile(r'product-price')}),
            ('div', {'class': re.compile(r'price.*current')}),
        ]
        
        for tag, attrs in selectors:
            elem = soup.find(tag, attrs)
            if elem:
                price = self.clean_price(elem.text.strip())
                if price:
                    return price
                    
        return None
        
    def extract_price_trendyol(self, html: str) -> Optional[float]:
        """Trendyol fiyat çıkarma"""
        soup = BeautifulSoup(html, 'html.parser')
        
        # Trendyol selectors
        selectors = [
            ('span', {'class': 'prc-dsc'}),
            ('span', {'class': 'prc-slg'}),
            ('div', {'class': re.compile(r'pr-bx-pr')}),
        ]
        
        for tag, attrs in selectors:
            elem = soup.find(tag, attrs)
            if elem:
                price = self.clean_price(elem.text.strip())
                if price:
                    return price
                    
        return None
        
    def extract_price_teknosa(self, html: str) -> Optional[float]:
        """Teknosa fiyat çıkarma"""
        soup = BeautifulSoup(html, 'html.parser')
        
        # Teknosa selectors
        price_elem = soup.find('span', class_='prc')
        if price_elem:
            return self.clean_price(price_elem.text.strip())
            
        # JavaScript'ten fiyat ara
        price_match = re.search(r'"price":\s*["\']?(\d+\.?\d*)["\']?', html)
        if price_match:
            return float(price_match.group(1))
            
        return None
        
    def extract_price_amazon(self, html: str) -> Optional[float]:
        """Amazon TR fiyat çıkarma"""
        soup = BeautifulSoup(html, 'html.parser')
        
        # Amazon selectors
        selectors = [
            ('span', {'class': 'a-price-whole'}),
            ('span', {'id': 'priceblock_ourprice'}),
            ('span', {'id': 'priceblock_dealprice'}),
            ('span', {'class': re.compile(r'a-price(?!.*a-text-price)')}),
        ]
        
        for tag, attrs in selectors:
            elem = soup.find(tag, attrs)
            if elem:
                price = self.clean_price(elem.text.strip())
                if price:
                    return price
                    
        return None
        
    def extract_price_vatanbilgisayar(self, html: str) -> Optional[float]:
        """Vatan Bilgisayar fiyat çıkarma"""
        soup = BeautifulSoup(html, 'html.parser')
        
        # Vatan selectors
        selectors = [
            ('span', {'class': 'product-price__price'}),
            ('div', {'class': 'product-price'}),
            ('span', {'class': re.compile(r'price.*current')}),
        ]
        
        for tag, attrs in selectors:
            elem = soup.find(tag, attrs)
            if elem:
                price = self.clean_price(elem.text.strip())
                if price:
                    return price
                    
        return None
        
    def extract_price_generic(self, html: str) -> Optional[float]:
        """Genel fiyat çıkarma"""
        soup = BeautifulSoup(html, 'html.parser')
        
        # Genel selectors
        selectors = [
            ('span', {'class': re.compile(r'price')}),
            ('div', {'class': re.compile(r'price')}),
            ('meta', {'property': 'product:price:amount'}),
            ('span', {'itemprop': 'price'}),
        ]
        
        for tag, attrs in selectors:
            elem = soup.find(tag, attrs)
            if elem:
                if tag == 'meta':
                    content = elem.get('content')
                    if content:
                        try:
                            return float(content)
                        except:
                            pass
                else:
                    price = self.clean_price(elem.text.strip())
                    if price:
                        return price
                        
        # Regex ile TL ara
        price_matches = re.findall(r'(\d{1,5}[.,]?\d*)\s*(?:TL|₺)', html)
        if price_matches:
            for match in price_matches:
                price = self.clean_price(match)
                if price and 10 < price < 100000:  # Mantıklı fiyat aralığı
                    return price
                    
        return None
        
    def extract_price(self, html: str, supplier_name: str) -> Optional[float]:
        """Supplier'a göre fiyat çıkar"""
        extractors = {
            'Hepsiburada': self.extract_price_hepsiburada,
            'Trendyol': self.extract_price_trendyol,
            'Teknosa': self.extract_price_teknosa,
            'Amazon TR': self.extract_price_amazon,
            'Vatan Bilgisayar': self.extract_price_vatanbilgisayar,
            'MediaMarkt': self.extract_price_generic,
            'N11': self.extract_price_generic,
            'GittiGidiyor': self.extract_price_generic,
            'Çiçeksepeti': self.extract_price_generic,
            'Morhipo': self.extract_price_generic,
            'Genel Tedarikçi': self.extract_price_generic,
        }
        
        extractor = extractors.get(supplier_name, self.extract_price_generic)
        return extractor(html)
        
    def update_price(self, ps_id: int, price: float):
        """Veritabanında fiyatı güncelle"""
        conn = self.get_db_connection()
        cursor = conn.cursor()
        cursor.execute("""
            UPDATE product_suppliers 
            SET supplier_price = ?, last_checked = ? 
            WHERE id = ?
        """, (price, datetime.now(), ps_id))
        conn.commit()
        conn.close()
        logger.info(f"Fiyat güncellendi - PS ID: {ps_id}, Fiyat: {price}")
        
    def update_product_aggregates(self, product_id: int):
        """Ürün min/max/avg fiyatlarını güncelle"""
        conn = self.get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT supplier_price 
            FROM product_suppliers 
            WHERE product_id = ? 
            AND is_deleted = 0 
            AND supplier_price IS NOT NULL 
            AND supplier_price > 0
        """, (product_id,))
        
        prices = [row[0] for row in cursor.fetchall()]
        
        if prices:
            min_price = min(prices)
            max_price = max(prices)
            avg_price = sum(prices) / len(prices)
            
            cursor.execute("""
                UPDATE products 
                SET min_price = ?, max_price = ?, avg_price = ?, price_updated_at = ? 
                WHERE id = ?
            """, (min_price, max_price, avg_price, datetime.now(), product_id))
            conn.commit()
            logger.info(f"Product {product_id} fiyatları güncellendi - Min: {min_price}, Max: {max_price}, Avg: {avg_price:.2f}")
        
        conn.close()
        
    def process_url(self, ps_id: int, product_id: int, url: str, supplier_name: str) -> bool:
        """Tek bir URL'yi işle"""
        try:
            print(f"\n{'='*60}")
            print(f"Supplier: {supplier_name}")
            print(f"URL: {url}")
            print("Sayfa yükleniyor...")
            
            self.driver.get(url)
            
            print("\nTALİMATLAR:")
            print("1. Sayfa tam yüklenene kadar bekleyin")
            print("2. CAPTCHA varsa çözün")
            print("3. Login gerekiyorsa giriş yapın")
            print("4. Hazır olunca Enter'a basın (İptal için 'skip' yazın)")
            
            user_input = input("\n> ").strip().lower()
            
            if user_input == 'skip':
                print("Atlandı.")
                return False
                
            # HTML'i al
            html = self.driver.page_source
            
            # Fiyat çıkar
            price = self.extract_price(html, supplier_name)
            
            if price:
                print(f"✓ Fiyat bulundu: {price} TL")
                self.update_price(ps_id, price)
                return True
            else:
                print("✗ Fiyat bulunamadı")
                
                # Debug için
                debug = input("HTML'de fiyat aramak ister misiniz? (e/h): ").strip().lower()
                if debug == 'e':
                    # TL içeren satırları göster
                    lines = html.split('\n')
                    tl_lines = [line for line in lines if 'TL' in line or '₺' in line]
                    print(f"\nTL içeren {len(tl_lines)} satır bulundu:")
                    for line in tl_lines[:10]:  # İlk 10 satır
                        print(line.strip()[:100])
                        
                return False
                
        except Exception as e:
            logger.error(f"Hata: {str(e)}")
            return False
            
    def run(self):
        """Ana çalıştırma metodu"""
        print("\n" + "="*60)
        print("INTERACTIVE PRICE SCRAPER")
        print("="*60)
        
        product_suppliers = self.get_product_suppliers()
        total = len(product_suppliers)
        
        print(f"\nToplam {total} URL işlenecek.")
        
        processed_products = set()
        success_count = 0
        
        for i, (ps_id, product_id, url, supplier_name) in enumerate(product_suppliers, 1):
            print(f"\n[{i}/{total}] İşleniyor...")
            
            if self.process_url(ps_id, product_id, url, supplier_name):
                success_count += 1
                processed_products.add(product_id)
                
            # Her 5 URL'de bir mola
            if i % 5 == 0 and i < total:
                print("\nMola vermek ister misiniz? (Enter: devam, 'q': çık)")
                if input().strip().lower() == 'q':
                    break
                    
        # Ürün fiyatlarını güncelle
        print("\n\nÜrün fiyatları güncelleniyor...")
        for product_id in processed_products:
            self.update_product_aggregates(product_id)
            
        print(f"\n{'='*60}")
        print(f"ÖZET: {success_count}/{total} fiyat başarıyla güncellendi")
        print("="*60)
        
        # Browser'ı kapat
        if self.driver:
            self.driver.quit()
            print("\nBrowser kapatıldı.")

if __name__ == "__main__":
    scraper = InteractivePriceScraper()
    try:
        scraper.run()
    except KeyboardInterrupt:
        print("\n\nİptal edildi.")
        if scraper.driver:
            scraper.driver.quit()
    except Exception as e:
        logger.error(f"Beklenmeyen hata: {str(e)}")
        if scraper.driver:
            scraper.driver.quit()
        raise