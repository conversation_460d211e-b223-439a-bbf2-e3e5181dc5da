#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fiyat geçmişi görüntüleme ve analiz scripti
"""

import logging
from database import Database
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import pandas as pd
from typing import List, Dict

# Logging konfigürasyonu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def show_price_history(product_supplier_id: int, days: int = 30):
    """<PERSON><PERSON>li bir ürün-tedarikçi için fiyat geçmişini göster"""
    
    db = Database()
    history = db.get_price_history(product_supplier_id, days)
    
    if not history:
        print(f"❌ Product Supplier ID {product_supplier_id} için fiyat geçmişi bulunamadı")
        return
    
    print(f"\n📊 {history[0]['product_name']} - {history[0]['supplier_name']}")
    print(f"📅 Son {days} günün fiyat geçmişi:")
    print("=" * 80)
    
    for record in history:
        date = datetime.strptime(record['created_at'], '%Y-%m-%d %H:%M:%S')
        change_icon = "📈" if record['price_change'] > 0 else "📉" if record['price_change'] < 0 else "➡️"
        
        print(f"{date.strftime('%Y-%m-%d %H:%M')} | {record['price']:.2f} TL", end="")
        
        if record['old_price']:
            print(f" | Önceki: {record['old_price']:.2f} TL", end="")
            print(f" | Değişim: {change_icon} {record['price_change']:+.2f} TL ({record['price_change_percent']:+.1f}%)")
        else:
            print(" | İlk kayıt")

def show_product_trends(product_id: int, days: int = 30):
    """Bir ürünün tüm tedarikçilerindeki fiyat trendlerini göster"""
    
    db = Database()
    trends = db.get_product_price_trends(product_id, days)
    
    if not trends['suppliers']:
        print(f"❌ Product ID {product_id} için fiyat trendi bulunamadı")
        return
    
    print(f"\n📈 Product ID {product_id} - Son {days} günün fiyat trendleri:")
    print("=" * 100)
    
    for supplier in trends['suppliers']:
        print(f"\n🏪 {supplier['supplier_name']}")
        print(f"   💰 Güncel Fiyat: {supplier['current_price']:.2f} TL" if supplier['current_price'] else "   💰 Güncel Fiyat: Bilinmiyor")
        
        if supplier['price_changes'] > 0:
            print(f"   📊 Dönem İstatistikleri:")
            print(f"      • Fiyat Değişimi: {supplier['price_changes']} kez")
            print(f"      • En Düşük: {supplier['min_price_period']:.2f} TL")
            print(f"      • En Yüksek: {supplier['max_price_period']:.2f} TL")
            print(f"      • Ortalama: {supplier['avg_price_period']:.2f} TL")
        else:
            print(f"   📊 Bu dönemde fiyat değişimi yok")

def show_price_alerts(threshold: float = 10.0):
    """Önemli fiyat değişimlerini göster"""
    
    db = Database()
    alerts = db.get_price_alerts(threshold)
    
    if not alerts:
        print(f"❌ Son 7 günde %{threshold} üzeri fiyat değişimi bulunamadı")
        return
    
    print(f"\n🚨 Son 7 günün önemli fiyat değişimleri (>%{threshold}):")
    print("=" * 120)
    
    for alert in alerts:
        date = datetime.strptime(alert['created_at'], '%Y-%m-%d %H:%M:%S')
        change_icon = "📈" if alert['price_change'] > 0 else "📉"
        urgency = "🔥" if abs(alert['price_change_percent']) > 25 else "⚠️"
        
        print(f"{urgency} {date.strftime('%Y-%m-%d %H:%M')} | {alert['product_name'][:40]:<40} | {alert['supplier_name']:<15}")
        print(f"   {change_icon} {alert['old_price']:.2f} TL → {alert['price']:.2f} TL | Değişim: {alert['price_change']:+.2f} TL ({alert['price_change_percent']:+.1f}%)")
        print()

def show_all_current_prices():
    """Tüm güncel fiyatları göster"""

    db = Database()
    suppliers = db.get_all_product_suppliers()

    if not suppliers:
        print("❌ Ürün bulunamadı")
        return

    print(f"\n💰 Tüm Güncel Fiyatlar:")
    print("=" * 120)

    # Ürünlere göre grupla
    products = {}
    for supplier in suppliers:
        product_id = supplier['product_id']
        if product_id not in products:
            products[product_id] = {
                'name': supplier['product_name'],
                'sku': supplier['sku'],
                'suppliers': []
            }
        products[product_id]['suppliers'].append(supplier)

    for product_id, product_data in products.items():
        print(f"\n📦 {product_data['name']} (SKU: {product_data['sku']})")

        # Fiyatları sırala
        suppliers_with_price = [s for s in product_data['suppliers'] if s['supplier_price']]
        suppliers_with_price.sort(key=lambda x: x['supplier_price'])

        if suppliers_with_price:
            min_price = suppliers_with_price[0]['supplier_price']
            max_price = suppliers_with_price[-1]['supplier_price']

            print(f"   💡 Fiyat Aralığı: {min_price:.2f} TL - {max_price:.2f} TL")

            for supplier in suppliers_with_price:
                price_indicator = "🟢" if supplier['supplier_price'] == min_price else "🔴" if supplier['supplier_price'] == max_price else "🟡"
                print(f"   {price_indicator} {supplier['supplier_price']:.2f} TL - Tedarikçi: Bilinmiyor")
        else:
            print(f"   ❌ Hiçbir tedarikçide fiyat bilgisi yok")

def show_product_price_statistics():
    """Products tablosundaki fiyat istatistiklerini göster"""

    db = Database()
    products = db.get_products_with_price_stats()

    if not products:
        print("❌ Ürün bulunamadı")
        return

    print(f"\n📊 Ürün Fiyat İstatistikleri (Products Tablosu):")
    print("=" * 140)
    print(f"{'Ürün Adı':<40} {'SKU':<20} {'Min':<10} {'Max':<10} {'Ort':<10} {'Tedarikçi':<10} {'Güncelleme':<20}")
    print("-" * 140)

    for product in products:
        name = product['product_name'][:37] + "..." if len(product['product_name']) > 40 else product['product_name']
        sku = product['sku'][:17] + "..." if product['sku'] and len(product['sku']) > 20 else (product['sku'] or "N/A")

        min_price = f"{product['min_price']:.2f}" if product['min_price'] else "N/A"
        max_price = f"{product['max_price']:.2f}" if product['max_price'] else "N/A"
        avg_price = f"{product['avg_price']:.2f}" if product['avg_price'] else "N/A"

        supplier_info = f"{product['active_suppliers']}/{product['supplier_count']}"

        updated_at = "N/A"
        if product['price_updated_at']:
            try:
                from datetime import datetime
                dt = datetime.strptime(product['price_updated_at'], '%Y-%m-%d %H:%M:%S')
                updated_at = dt.strftime('%Y-%m-%d %H:%M')
            except:
                updated_at = str(product['price_updated_at'])[:16]

        print(f"{name:<40} {sku:<20} {min_price:<10} {max_price:<10} {avg_price:<10} {supplier_info:<10} {updated_at:<20}")

    print(f"\n📈 Özet:")
    print(f"   • Toplam ürün: {len(products)}")
    products_with_prices = [p for p in products if p['min_price'] is not None]
    print(f"   • Fiyatı olan ürün: {len(products_with_prices)}")
    if products_with_prices:
        all_min_prices = [p['min_price'] for p in products_with_prices]
        all_max_prices = [p['max_price'] for p in products_with_prices]
        print(f"   • En düşük fiyat: {min(all_min_prices):.2f} TL")
        print(f"   • En yüksek fiyat: {max(all_max_prices):.2f} TL")

def main():
    """Ana menü"""
    
    while True:
        print("\n" + "="*60)
        print("📊 FİYAT GEÇMİŞİ VE ANALİZ SİSTEMİ")
        print("="*60)
        print("1. 📈 Ürün-Tedarikçi Fiyat Geçmişi")
        print("2. 📊 Ürün Fiyat Trendleri")
        print("3. 🚨 Fiyat Uyarıları")
        print("4. 💰 Tüm Güncel Fiyatlar")
        print("5. 📊 Ürün Fiyat İstatistikleri (Products Tablosu)")
        print("6. 🚪 Çıkış")

        choice = input("\nSeçiminizi yapın (1-6): ").strip()
        
        if choice == "1":
            try:
                ps_id = int(input("Product Supplier ID girin: "))
                days = int(input("Kaç günlük geçmiş? (varsayılan 30): ") or "30")
                show_price_history(ps_id, days)
            except ValueError:
                print("❌ Geçerli bir sayı girin")
        
        elif choice == "2":
            try:
                product_id = int(input("Product ID girin: "))
                days = int(input("Kaç günlük trend? (varsayılan 30): ") or "30")
                show_product_trends(product_id, days)
            except ValueError:
                print("❌ Geçerli bir sayı girin")
        
        elif choice == "3":
            try:
                threshold = float(input("Minimum değişim yüzdesi? (varsayılan 10): ") or "10")
                show_price_alerts(threshold)
            except ValueError:
                print("❌ Geçerli bir sayı girin")
        
        elif choice == "4":
            show_all_current_prices()

        elif choice == "5":
            show_product_price_statistics()

        elif choice == "6":
            print("👋 Görüşmek üzere!")
            break
        
        else:
            print("❌ Geçersiz seçim")

if __name__ == "__main__":
    main()
