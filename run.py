#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Veritabanından ürün linklerini çeken script
"""

import logging
import time
import re
from database import Database
from typing import List, Dict
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from bs4 import BeautifulSoup

# Logging konfigürasyonu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def extract_price(html: str, url: str = "") -> float:
    """
    HTML içeriğinden fiyat bilgisini çıkarır

    Args:
        html (str): Sayfa HTML içeriği
        url (str): Sayfa URL'si (site tespiti için)

    Returns:
        float: Bulunan fiyat, bulunamazsa 0.0
    """
    try:
        soup = BeautifulSoup(html, 'html.parser')

        # Farklı siteler için fiyat selektörleri (analiz sonuçlarına göre güncellenmiş)
        price_selectors = {
            'hepsiburada.com': [
                '.price-current',
                '.price',
                '[data-test-id="price-current-price"]',
                '.product-price'
            ],
            'trendyol.com': [
                '.prc-dsc',
                '.prc-org',
                '.price-current',
                '.product-price'
            ],
            'teknosa.com': [
                # Önce en spesifik fiyat selektörlerini dene
                '.pds-prices',  # Ana ürün fiyatı
                '.swogo-price-with-discount',  # İndirimli fiyat
                '.swogo-price:not(.swogo-price-with-discount)',  # Normal fiyat
                '[class*="price"]:not([class*="alarm"]):not([class*="track"])',  # Fiyat içeren ama alarm/track olmayan
                '.col-12:contains("TL"):not(:contains("Sepete"))',  # TL içeren ama sepete ekleme butonu olmayan
                '.product-price',
                '.price',
                '.current-price'
            ],
            'mediamarkt.com.tr': [
                # Önce daha spesifik fiyat selektörlerini dene
                '[data-test-id*="price"]',
                '.price-box .price',
                '.product-price-box .price',
                '.sc-e0c7d9f7-0.bPkjPs:contains("₺")',  # Sadece ₺ içerenler
                '.sc-d571b66f-0.kMgwbl.sc-9504f841-2.lgovom',
                '.price',
                '.product-price',
                '.current-price'
            ],
            'vatanbilgisayar.com': [
                '.product-list__price',  # Analiz sonucunda bulundu: "949"
                '.basketMobile_price',  # Analiz sonucunda bulundu: "949,00TL"
                '.d-cell',  # Analiz sonucunda bulundu: "949TL"
                '.product-list__content__price__current',
                '.price',
                '.current-price'
            ],
            'amazon.com.tr': [
                '.a-price-whole',
                '.a-price',
                '.price'
            ],
            'n11.com': [
                '.newPrice',
                '.priceContainer .price',
                '.productPrice .price',
                '.ins',
                '[class*="price"]'
            ],
            'gittigidiyor.com': [
                '.price-current',
                '.gg-price',
                '.product-price',
                '.price'
            ],
            'ciceksepeti.com': [
                '.price',
                '.product-price',
                '.current-price',
                '[class*="price"]'
            ],
            'morhipo.com': [
                '.price',
                '.product-price',
                '.current-price',
                '[class*="price"]'
            ]
        }

        # Site tespiti
        site_key = None
        for site in price_selectors.keys():
            if site in url.lower():
                site_key = site
                break

        # Fiyat arama
        price_text = None
        if site_key:
            # Belirli site için selektörleri dene
            for selector in price_selectors[site_key]:
                elements = soup.select(selector)
                if elements:
                    # Birden fazla element varsa, en uygun olanı seç
                    best_element = None
                    best_score = -1

                    for element in elements:
                        text = element.get_text(strip=True)
                        if not text:
                            continue

                        # Element kalite skoru hesapla
                        score = 0

                        # TL veya ₺ içeriyorsa +10
                        if re.search(r'[₺TL]', text):
                            score += 10

                        # Sayı içeriyorsa +5
                        if re.search(r'\d', text):
                            score += 5

                        # Kısa metinler daha iyi +5 (50 karakter altı)
                        if len(text) <= 50:
                            score += 5

                        # Çok uzun metinler kötü -10 (200 karakter üstü)
                        if len(text) > 200:
                            score -= 10

                        # Problemli kelimeler varsa -20
                        problematic_words = ['değerlendirme', 'inceleme', 'yorum', 'sepet', 'kargo']
                        if any(word in text.lower() for word in problematic_words):
                            score -= 20

                        if score > best_score:
                            best_score = score
                            best_element = element

                    if best_element and best_score > 0:
                        price_text = best_element.get_text(strip=True)
                        logger.info(f"Seçilen element (skor: {best_score}): {selector}")
                        break

        # Genel fiyat arama (site bulunamazsa)
        if not price_text:
            # Genel fiyat selektörleri
            general_selectors = [
                '.price', '.product-price', '.current-price', '.price-current',
                '[class*="price"]', '[class*="fiyat"]', '[id*="price"]'
            ]

            for selector in general_selectors:
                element = soup.select_one(selector)
                if element:
                    price_text = element.get_text(strip=True)
                    break

        if price_text:
            # Fiyat metninden sayısal değeri çıkar
            logger.info(f"Bulunan fiyat metni: '{price_text[:100]}...' - URL: {url}")

            # Çok uzun metinleri filtrele (muhtemelen yanlış element)
            if len(price_text) > 200:
                logger.warning(f"Çok uzun fiyat metni, muhtemelen yanlış element: {len(price_text)} karakter")
                return 0.0

            # Özel durumlar için fiyat çıkarma (Trendyol gibi)
            special_patterns = [
                r'son \d+ günün en düşük fiyatı[!]*(\d{1,4})\s*tl',  # "Son 14 Günün En Düşük Fiyatı!935 TL"
                r'en düşük fiyat[!]*(\d{1,4})\s*tl'  # "En Düşük Fiyat!935 TL"
            ]

            for pattern in special_patterns:
                match = re.search(pattern, price_text.lower())
                if match:
                    try:
                        price_value = float(match.group(1))
                        if 10 <= price_value <= 100000:
                            logger.info(f"Özel pattern ile çıkarılan fiyat: {price_value}")
                            return price_value
                    except ValueError:
                        continue

            # Diğer problemli metinleri filtrele
            problematic_patterns = [
                r'değerlendirme',
                r'inceleme',
                r'yorum',
                r'puan',
                r'sepet',
                r'kargo',
                r'teslimat',
                r'garanti',
                r'kampanya'
            ]

            for pattern in problematic_patterns:
                if re.search(pattern, price_text.lower()):
                    logger.warning(f"Problemli metin tespit edildi: {pattern}")
                    return 0.0

            # Farklı fiyat formatlarını destekle
            # Örnek formatlar: "1.197 TL", "₺949,00", "949TL", "1.258,–"

            # Önce sadece fiyat kısmını bul
            # TL, ₺ ile birlikte olan sayıları ara
            price_patterns = [
                r'(\d{1,3}(?:[.,]\d{3})*(?:[.,]\d{1,2})?)\s*[₺TL]',  # 1.234,56 TL veya ₺1.234,56
                r'[₺TL]\s*(\d{1,3}(?:[.,]\d{3})*(?:[.,]\d{1,2})?)',  # TL 1.234,56 veya ₺ 1.234,56
                r'(\d{1,3}(?:[.,]\d{3})*(?:[.,]\d{1,2})?)\s*(?:TL|₺|lira)',  # 1.234,56 TL
                r'(\d{1,3}(?:\.\d{3})*(?:,\d{1,2})?)\s*(?=\s|$|,)',  # Sadece sayı (Türk formatı) - virgül ile biten
                r'(\d{1,3}(?:,\d{3})*(?:\.\d{1,2})?)\s*(?=\s|$)',   # Sadece sayı (US formatı)
                r'(\d{1,4}),?\s*(?=\s|$)',  # Amazon formatı: "899," veya "1138,"
                r'(\d{1,4})\s*(?=\s|$)'     # Basit sayı formatı
            ]

            price_value = None
            for pattern in price_patterns:
                matches = re.findall(pattern, price_text, re.IGNORECASE)
                if matches:
                    # En büyük değeri al (genelde asıl fiyat)
                    for match in matches:
                        try:
                            # Türk formatını normalize et
                            clean_price = match.replace('.', '').replace(',', '.')
                            if clean_price.count('.') > 1:
                                # Birden fazla nokta varsa, son nokta ondalık ayırıcı
                                parts = clean_price.split('.')
                                clean_price = ''.join(parts[:-1]) + '.' + parts[-1]

                            value = float(clean_price)

                            # Makul fiyat aralığı kontrolü (10 TL - 100.000 TL)
                            if 10 <= value <= 100000:
                                # Daha akıllı fiyat seçimi: çok büyük fiyatları öncelikle reddet
                                if price_value is None:
                                    price_value = value
                                elif value < price_value and value > 50:  # Daha küçük ama makul fiyatı tercih et
                                    price_value = value
                                elif price_value > 10000 and value < 5000:  # Çok büyük fiyat varsa küçüğü tercih et
                                    price_value = value
                        except ValueError:
                            continue

                    if price_value:
                        break

            if price_value:
                logger.info(f"Çıkarılan fiyat: {price_value}")
                return price_value
            else:
                logger.warning(f"Geçerli fiyat bulunamadı: '{price_text[:50]}...'")
                return 0.0

        return 0.0

    except Exception as e:
        logger.error(f"Fiyat çıkarılırken hata: {e}")
        return 0.0

def get_product_links() -> List[Dict]:
    """
    Veritabanından tüm ürün linklerini çeker

    Returns:
        List[Dict]: Ürün bilgileri ve linkleri içeren liste
    """
    try:
        db = Database()
        product_suppliers = db.get_all_product_suppliers()

        logger.info(f"Toplam {len(product_suppliers)} ürün linki bulundu")

        # Sonuçları yazdır
        for item in product_suppliers:
            print(f"ID: {item['id']}")
            print(f"Ürün ID: {item['product_id']}")
            print(f"Ürün Adı: {item['product_name']}")
            print(f"SKU: {item['sku']}")
            print(f"Supplier URL: {item['supplier_product_url']}")
            print(f"Mevcut Fiyat: {item['supplier_price']}")
            print("-" * 50)

        return product_suppliers

    except Exception as e:
        logger.error(f"Ürün linkleri çekilirken hata oluştu: {e}")
        return []

def get_product_links_by_supplier(supplier_name: str) -> List[Dict]:
    """
    Belirli bir tedarikçiye ait ürün linklerini çeker

    Args:
        supplier_name (str): Tedarikçi adı

    Returns:
        List[Dict]: Tedarikçiye ait ürün linkleri
    """
    try:
        from scrapers.base_scraper import BaseScraper

        # Geçici bir scraper instance oluştur
        scraper = BaseScraper(supplier_name)
        links = scraper.get_product_supplier_links()

        logger.info(f"{supplier_name} için {len(links)} ürün linki bulundu")

        # Sonuçları yazdır
        for link in links:
            print(f"PS ID: {link[0]}")
            print(f"Ürün ID: {link[1]}")
            print(f"URL: {link[2]}")
            print("-" * 30)

        return links

    except Exception as e:
        logger.error(f"{supplier_name} için ürün linkleri çekilirken hata: {e}")
        return []

def analyze_page_structure(url: str, site_name: str):
    """
    Sayfa yapısını analiz eder ve fiyat elementlerini bulur
    """
    print(f"\n🔍 {site_name} sayfa yapısı analiz ediliyor...")
    print(f"URL: {url}")

    chrome_options = Options()
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument('--start-maximized')

    browser = None
    try:
        browser = webdriver.Chrome(options=chrome_options)
        browser.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        print("📄 Sayfa yükleniyor...")
        browser.get(url)
        time.sleep(5)

        html = browser.page_source
        soup = BeautifulSoup(html, 'html.parser')

        # Fiyat içerebilecek elementleri ara
        price_candidates = []

        # Fiyat kelimesi içeren elementler
        price_keywords = ['price', 'fiyat', 'tutar', 'amount', 'cost']
        for keyword in price_keywords:
            elements = soup.find_all(attrs={'class': re.compile(keyword, re.I)})
            elements += soup.find_all(attrs={'id': re.compile(keyword, re.I)})
            for elem in elements:
                text = elem.get_text(strip=True)
                if text and re.search(r'\d+[.,]\d+|\d+', text):
                    price_candidates.append({
                        'selector': f".{elem.get('class', [''])[0]}" if elem.get('class') else f"#{elem.get('id', '')}",
                        'text': text[:100],
                        'tag': elem.name
                    })

        # TL, ₺ sembolü içeren elementler
        currency_elements = soup.find_all(string=re.compile(r'[₺TL]|\d+[.,]\d+'))
        for elem in currency_elements:
            parent = elem.parent
            if parent:
                text = parent.get_text(strip=True)
                if re.search(r'\d+[.,]\d+', text):
                    classes = parent.get('class', [])
                    class_str = '.'.join(classes) if classes else ''
                    price_candidates.append({
                        'selector': f".{class_str}" if class_str else parent.name,
                        'text': text[:100],
                        'tag': parent.name
                    })

        # Sonuçları göster
        print(f"\n📋 {site_name} için bulunan fiyat adayları:")
        unique_candidates = []
        seen_texts = set()

        for candidate in price_candidates:
            if candidate['text'] not in seen_texts:
                seen_texts.add(candidate['text'])
                unique_candidates.append(candidate)

        for i, candidate in enumerate(unique_candidates[:10], 1):  # İlk 10 adayı göster
            print(f"{i}. Selector: {candidate['selector']}")
            print(f"   Text: {candidate['text']}")
            print(f"   Tag: {candidate['tag']}")
            print("-" * 50)

        if not unique_candidates:
            print("❌ Fiyat adayı bulunamadı")

            # Sayfa başlığını ve genel yapıyı kontrol et
            title = soup.find('title')
            print(f"📄 Sayfa başlığı: {title.get_text() if title else 'Bulunamadı'}")

            # Tüm class'ları listele
            all_classes = set()
            for elem in soup.find_all(class_=True):
                all_classes.update(elem['class'])

            price_related_classes = [cls for cls in all_classes if any(keyword in cls.lower() for keyword in ['price', 'fiyat', 'tutar', 'amount'])]
            if price_related_classes:
                print(f"💡 Fiyat ile ilgili class'lar: {price_related_classes[:10]}")

        return unique_candidates

    except Exception as e:
        logger.error(f"Sayfa analizi sırasında hata: {e}")
        print(f"❌ Hata: {e}")
        return []

    finally:
        if browser:
            browser.quit()

def show_price_history_summary():
    """Fiyat geçmişi özetini göster"""
    try:
        from database import Database
        db = Database()

        # Son 7 günün fiyat uyarıları
        alerts = db.get_price_alerts(5.0)  # %5 üzeri değişimler

        if alerts:
            print(f"\n🚨 Son 7 günün önemli fiyat değişimleri:")
            print("-" * 60)

            for alert in alerts[:5]:  # İlk 5 uyarı
                change_icon = "📈" if alert['price_change'] > 0 else "📉"
                print(f"{change_icon} {alert['product_name'][:30]:<30} | {alert['supplier_name']:<12}")
                print(f"   {alert['old_price']:.2f} TL → {alert['price']:.2f} TL ({alert['price_change_percent']:+.1f}%)")

            if len(alerts) > 5:
                print(f"   ... ve {len(alerts)-5} değişim daha")
        else:
            print("\n✅ Son 7 günde önemli fiyat değişimi yok")

    except Exception as e:
        logger.error(f"Fiyat geçmişi özeti gösterilirken hata: {e}")

def main():
    """Ana fonksiyon"""
    print("=== 🛒 PAZARYERI FİYAT TAKİP SİSTEMİ ===\n")

    # Fiyat geçmişi özeti
    show_price_history_summary()

    # Tüm ürün linklerini çek
    print("\n1. 📦 Tüm ürün linkleri çekiliyor...")
    all_links = get_product_links()

    # Tüm linkler için fiyat çekme testi
    if all_links:
        print(f"\n🔍 Tüm linkler için fiyat çekme testi başlatılıyor...")
        print(f"Toplam {len(all_links)} link test edilecek\n")

        # Chrome seçenekleri
        chrome_options = Options()
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        chrome_options.add_argument('--start-maximized')
        # chrome_options.add_argument('--headless')  # Arka planda çalıştırmak için

        browser = None
        successful_extractions = 0
        failed_extractions = 0

        try:
            browser = webdriver.Chrome(options=chrome_options)
            browser.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            for i, link in enumerate(all_links, 1):
                url = link['supplier_product_url']
                product_name = link['product_name'][:50] + "..." if len(link['product_name']) > 50 else link['product_name']

                print(f"[{i}/{len(all_links)}] Test ediliyor: {product_name}")
                print(f"URL: {url}")

                try:
                    print("📄 Sayfa yükleniyor...")
                    browser.get(url)
                    time.sleep(3)  # Sayfanın yüklenmesini bekle

                    html = browser.page_source
                    price = extract_price(html, url)

                    if price > 0:
                        print(f"✅ Fiyat bulundu: {price} TL")
                        successful_extractions += 1

                        # Veritabanını güncelle
                        from database import Database
                        db = Database()
                        db.update_supplier_price(link['id'], price)
                        print(f"💾 Veritabanı güncellendi (ID: {link['id']})")

                        # Ürün fiyat istatistiklerini güncelle
                        product_id = link['product_id']
                        db.update_product_price_statistics(product_id)
                        print(f"📊 Ürün fiyat istatistikleri güncellendi (Product ID: {product_id})")

                    else:
                        print("❌ Fiyat çıkarılamadı")
                        failed_extractions += 1

                except Exception as e:
                    logger.error(f"Link işlenirken hata: {e}")
                    print(f"❌ Hata: {e}")
                    failed_extractions += 1

                print("-" * 80)

                # Siteler arası nezaket bekleme süresi
                if i < len(all_links):
                    print("⏳ Sonraki link için bekleniyor...")
                    time.sleep(2)

        except Exception as e:
            logger.error(f"Browser başlatılırken hata: {e}")
            print(f"❌ Browser hatası: {e}")

        finally:
            if browser:
                browser.quit()
                print("🔒 Browser kapatıldı.")

        # Özet rapor
        print(f"\n📊 ÖZET RAPOR:")
        print(f"✅ Başarılı: {successful_extractions}")
        print(f"❌ Başarısız: {failed_extractions}")
        print(f"📈 Başarı oranı: {(successful_extractions/len(all_links)*100):.1f}%")

    


    print(f"\nToplam {len(all_links)} ürün linki bulundu.\n")

    # Güncellenmiş fiyat çekme sistemini test et
    print("🧪 Güncellenmiş fiyat çekme sistemini test ediliyor...\n")

    test_sites = [
        {
            'name': 'MediaMarkt',
            'url': 'https://www.mediamarkt.com.tr/tr/product/_philips-mg372015-7si-1-arada-erkek-bakim-seti-1180484.html'
        },
        {
            'name': 'Vatan Bilgisayar',
            'url': 'https://www.vatanbilgisayar.com/philips-mg3720-15-3000-serisi-7-si1-arada-erkek-bakim-kiti.html'
        },
        {
            'name': 'Teknosa',
            'url': 'https://www.teknosa.com/philips-mg372015-7-si-1-arada-erkek-bakim-seti-p-120240912'
        }
    ]

    # Chrome seçenekleri
    chrome_options = Options()
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument('--start-maximized')

    browser = None
    try:
        browser = webdriver.Chrome(options=chrome_options)
        browser.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        for site in test_sites:
            print(f"🔍 {site['name']} test ediliyor...")
            print(f"URL: {site['url']}")

            try:
                browser.get(site['url'])
                time.sleep(4)

                html = browser.page_source
                price = extract_price(html, site['url'])

                if price > 0:
                    print(f"✅ Fiyat başarıyla çıkarıldı: {price} TL")
                else:
                    print(f"❌ Fiyat çıkarılamadı")

            except Exception as e:
                print(f"❌ Hata: {e}")

            print("-" * 60)
            time.sleep(2)

    except Exception as e:
        print(f"❌ Browser hatası: {e}")

    finally:
        if browser:
            browser.quit()
            print("🔒 Browser kapatıldı.")

if __name__ == "__main__":
    main()
