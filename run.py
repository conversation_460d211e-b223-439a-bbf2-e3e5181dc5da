#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Veritabanından ürün linklerini çeken script
"""

import logging
import time
import re
from database import Database
from typing import List, Dict
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from bs4 import BeautifulSoup

# Logging konfigürasyonu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def extract_price(html: str, url: str = "") -> float:
    """
    HTML içeriğinden fiyat bilgisini çıkarır

    Args:
        html (str): Sayfa HTML içeriği
        url (str): Sayfa URL'si (site tespiti için)

    Returns:
        float: Bulunan fiyat, bulunamazsa 0.0
    """
    try:
        soup = BeautifulSoup(html, 'html.parser')

        # Farklı siteler için fiyat selektörleri
        price_selectors = {
            'hepsiburada.com': [
                '.price-current',
                '.price',
                '[data-test-id="price-current-price"]',
                '.product-price'
            ],
            'trendyol.com': [
                '.prc-dsc',
                '.prc-org',
                '.price-current',
                '.product-price'
            ],
            'teknosa.com': [
                '.product-price',
                '.price',
                '.current-price'
            ],
            'mediamarkt.com.tr': [
                '.price',
                '.product-price',
                '.current-price'
            ],
            'vatanbilgisayar.com': [
                '.product-list__content__price__current',
                '.price',
                '.current-price'
            ],
            'amazon.com.tr': [
                '.a-price-whole',
                '.a-price',
                '.price'
            ]
        }

        # Site tespiti
        site_key = None
        for site in price_selectors.keys():
            if site in url.lower():
                site_key = site
                break

        # Fiyat arama
        price_text = None
        if site_key:
            # Belirli site için selektörleri dene
            for selector in price_selectors[site_key]:
                element = soup.select_one(selector)
                if element:
                    price_text = element.get_text(strip=True)
                    break

        # Genel fiyat arama (site bulunamazsa)
        if not price_text:
            # Genel fiyat selektörleri
            general_selectors = [
                '.price', '.product-price', '.current-price', '.price-current',
                '[class*="price"]', '[class*="fiyat"]', '[id*="price"]'
            ]

            for selector in general_selectors:
                element = soup.select_one(selector)
                if element:
                    price_text = element.get_text(strip=True)
                    break

        if price_text:
            # Fiyat metninden sayısal değeri çıkar
            # Türkçe karakterleri ve sembolleri temizle
            price_clean = re.sub(r'[^\d,.]', '', price_text)
            price_clean = price_clean.replace(',', '.')

            # Sayısal değeri al
            price_match = re.search(r'(\d+\.?\d*)', price_clean)
            if price_match:
                return float(price_match.group(1))

        return 0.0

    except Exception as e:
        logger.error(f"Fiyat çıkarılırken hata: {e}")
        return 0.0

def get_product_links() -> List[Dict]:
    """
    Veritabanından tüm ürün linklerini çeker

    Returns:
        List[Dict]: Ürün bilgileri ve linkleri içeren liste
    """
    try:
        db = Database()
        product_suppliers = db.get_all_product_suppliers()

        logger.info(f"Toplam {len(product_suppliers)} ürün linki bulundu")

        # Sonuçları yazdır
        for item in product_suppliers:
            print(f"ID: {item['id']}")
            print(f"Ürün ID: {item['product_id']}")
            print(f"Ürün Adı: {item['product_name']}")
            print(f"SKU: {item['sku']}")
            print(f"Supplier URL: {item['supplier_product_url']}")
            print(f"Mevcut Fiyat: {item['supplier_price']}")
            print("-" * 50)

        return product_suppliers

    except Exception as e:
        logger.error(f"Ürün linkleri çekilirken hata oluştu: {e}")
        return []

def get_product_links_by_supplier(supplier_name: str) -> List[Dict]:
    """
    Belirli bir tedarikçiye ait ürün linklerini çeker

    Args:
        supplier_name (str): Tedarikçi adı

    Returns:
        List[Dict]: Tedarikçiye ait ürün linkleri
    """
    try:
        from scrapers.base_scraper import BaseScraper

        # Geçici bir scraper instance oluştur
        scraper = BaseScraper(supplier_name)
        links = scraper.get_product_supplier_links()

        logger.info(f"{supplier_name} için {len(links)} ürün linki bulundu")

        # Sonuçları yazdır
        for link in links:
            print(f"PS ID: {link[0]}")
            print(f"Ürün ID: {link[1]}")
            print(f"URL: {link[2]}")
            print("-" * 30)

        return links

    except Exception as e:
        logger.error(f"{supplier_name} için ürün linkleri çekilirken hata: {e}")
        return []

def main():
    """Ana fonksiyon"""
    print("=== Veritabanından Ürün Linkleri ===\n")

    # Tüm ürün linklerini çek
    print("1. Tüm ürün linkleri çekiliyor...")
    all_links = get_product_links()

    # Fiyat çekme testi (sadece ilk link için)
    if all_links:
        link = all_links[0]  # İlk linki al
        url = link['supplier_product_url']

        print(f"\nFiyat çekme testi başlatılıyor...")
        print(f"URL: {url}")

        # Chrome seçenekleri
        chrome_options = Options()
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        chrome_options.add_argument('--start-maximized')
        # chrome_options.add_argument('--headless')  # Arka planda çalıştırmak için

        browser = None
        try:
            browser = webdriver.Chrome(options=chrome_options)
            browser.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            print("Sayfa yükleniyor...")
            browser.get(url)
            time.sleep(5)  # Sayfanın yüklenmesini bekle

            html = browser.page_source
            price = extract_price(html, url)

            print(f"Çıkarılan fiyat: {price} TL")

            if price > 0:
                print("✅ Fiyat başarıyla çıkarıldı!")
            else:
                print("❌ Fiyat çıkarılamadı")

        except Exception as e:
            logger.error(f"Fiyat çekme sırasında hata: {e}")
            print(f"❌ Hata: {e}")

        finally:
            if browser:
                browser.quit()
                print("Browser kapatıldı.")

    


    print(f"\nToplam {len(all_links)} ürün linki bulundu.\n")

    # İsteğe bağlı: Belirli bir tedarikçinin linklerini çek
    # supplier_name = "Hepsiburada"  # Örnek tedarikçi adı
    # print(f"2. {supplier_name} ürün linkleri çekiliyor...")
    # supplier_links = get_product_links_by_supplier(supplier_name)

if __name__ == "__main__":
    main()
