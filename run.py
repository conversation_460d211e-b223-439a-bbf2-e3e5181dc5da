#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Veritabanından ürün linklerini çeken script
"""

import logging
import time
import re
from database import Database
from typing import List, Dict
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from bs4 import BeautifulSoup

# Logging konfigürasyonu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def extract_price(html: str, url: str = "") -> float:
    """
    HTML içeriğinden fiyat bilgisini çıkarır

    Args:
        html (str): Sayfa HTML içeriği
        url (str): Sayfa URL'si (site tespiti için)

    Returns:
        float: Bulunan fiyat, bulunamazsa 0.0
    """
    try:
        soup = BeautifulSoup(html, 'html.parser')

        # Farklı siteler için fiyat selektörleri (analiz sonuçlarına göre güncellenmiş)
        price_selectors = {
            'hepsiburada.com': [
                '.price-current',
                '.price',
                '[data-test-id="price-current-price"]',
                '.product-price'
            ],
            'trendyol.com': [
                '.prc-dsc',
                '.prc-org',
                '.price-current',
                '.product-price'
            ],
            'teknosa.com': [
                '.col-12',  # Analiz sonucunda bulundu: "1.197 TL"
                '.swogo-price',  # Analiz sonucunda bulundu: "1.198 TL", "999 TL"
                '.pds-prices',  # Analiz sonucunda bulundu: "1.299 TL"
                '.swogo-price-with-discount',  # İndirimli fiyat
                '.product-price',
                '.price',
                '.current-price'
            ],
            'mediamarkt.com.tr': [
                '.sc-e0c7d9f7-0.bPkjPs',  # Analiz sonucunda bulundu: "₺949,00", "₺1258,00"
                '.sc-d571b66f-0.kMgwbl.sc-9504f841-2.lgovom',  # Analiz sonucunda bulundu: "₺1.258,–"
                '.price',
                '.product-price',
                '.current-price'
            ],
            'vatanbilgisayar.com': [
                '.product-list__price',  # Analiz sonucunda bulundu: "949"
                '.basketMobile_price',  # Analiz sonucunda bulundu: "949,00TL"
                '.d-cell',  # Analiz sonucunda bulundu: "949TL"
                '.product-list__content__price__current',
                '.price',
                '.current-price'
            ],
            'amazon.com.tr': [
                '.a-price-whole',
                '.a-price',
                '.price'
            ]
        }

        # Site tespiti
        site_key = None
        for site in price_selectors.keys():
            if site in url.lower():
                site_key = site
                break

        # Fiyat arama
        price_text = None
        if site_key:
            # Belirli site için selektörleri dene
            for selector in price_selectors[site_key]:
                element = soup.select_one(selector)
                if element:
                    price_text = element.get_text(strip=True)
                    break

        # Genel fiyat arama (site bulunamazsa)
        if not price_text:
            # Genel fiyat selektörleri
            general_selectors = [
                '.price', '.product-price', '.current-price', '.price-current',
                '[class*="price"]', '[class*="fiyat"]', '[id*="price"]'
            ]

            for selector in general_selectors:
                element = soup.select_one(selector)
                if element:
                    price_text = element.get_text(strip=True)
                    break

        if price_text:
            # Fiyat metninden sayısal değeri çıkar
            logger.info(f"Bulunan fiyat metni: '{price_text}' - URL: {url}")

            # Farklı fiyat formatlarını destekle
            # Örnek formatlar: "1.197 TL", "₺949,00", "949TL", "1.258,–"

            # Önce TL, ₺ sembollerini kaldır
            price_clean = re.sub(r'[₺TL–]', '', price_text)

            # Virgül ve nokta işaretlerini normalize et
            # Türkiye'de binlik ayırıcı nokta, ondalık ayırıcı virgül
            if ',' in price_clean and '.' in price_clean:
                # Hem virgül hem nokta varsa, son virgül ondalık ayırıcıdır
                parts = price_clean.split(',')
                if len(parts) == 2 and len(parts[1]) <= 2:  # Son kısım 2 haneli ise ondalık
                    price_clean = parts[0].replace('.', '') + '.' + parts[1]
                else:
                    price_clean = price_clean.replace(',', '').replace('.', '')
            elif ',' in price_clean:
                # Sadece virgül varsa
                parts = price_clean.split(',')
                if len(parts) == 2 and len(parts[1]) <= 2:  # Ondalık ayırıcı
                    price_clean = parts[0] + '.' + parts[1]
                else:  # Binlik ayırıcı
                    price_clean = price_clean.replace(',', '')
            elif '.' in price_clean:
                # Sadece nokta varsa - Türkiye'de genelde binlik ayırıcı
                parts = price_clean.split('.')
                if len(parts) == 2 and len(parts[1]) <= 2 and len(parts[0]) <= 3:
                    # Küçük sayı ve 2 haneli son kısım = ondalık
                    pass  # Nokta ondalık ayırıcı olarak kalır
                else:
                    # Binlik ayırıcı olarak kullanılmış
                    price_clean = price_clean.replace('.', '')

            # Sadece rakamları ve ondalık noktayı bırak
            price_clean = re.sub(r'[^\d.]', '', price_clean)

            # Sayısal değeri al
            price_match = re.search(r'(\d+\.?\d*)', price_clean)
            if price_match:
                price_value = float(price_match.group(1))
                logger.info(f"Çıkarılan fiyat: {price_value}")
                return price_value

        return 0.0

    except Exception as e:
        logger.error(f"Fiyat çıkarılırken hata: {e}")
        return 0.0

def get_product_links() -> List[Dict]:
    """
    Veritabanından tüm ürün linklerini çeker

    Returns:
        List[Dict]: Ürün bilgileri ve linkleri içeren liste
    """
    try:
        db = Database()
        product_suppliers = db.get_all_product_suppliers()

        logger.info(f"Toplam {len(product_suppliers)} ürün linki bulundu")

        # Sonuçları yazdır
        for item in product_suppliers:
            print(f"ID: {item['id']}")
            print(f"Ürün ID: {item['product_id']}")
            print(f"Ürün Adı: {item['product_name']}")
            print(f"SKU: {item['sku']}")
            print(f"Supplier URL: {item['supplier_product_url']}")
            print(f"Mevcut Fiyat: {item['supplier_price']}")
            print("-" * 50)

        return product_suppliers

    except Exception as e:
        logger.error(f"Ürün linkleri çekilirken hata oluştu: {e}")
        return []

def get_product_links_by_supplier(supplier_name: str) -> List[Dict]:
    """
    Belirli bir tedarikçiye ait ürün linklerini çeker

    Args:
        supplier_name (str): Tedarikçi adı

    Returns:
        List[Dict]: Tedarikçiye ait ürün linkleri
    """
    try:
        from scrapers.base_scraper import BaseScraper

        # Geçici bir scraper instance oluştur
        scraper = BaseScraper(supplier_name)
        links = scraper.get_product_supplier_links()

        logger.info(f"{supplier_name} için {len(links)} ürün linki bulundu")

        # Sonuçları yazdır
        for link in links:
            print(f"PS ID: {link[0]}")
            print(f"Ürün ID: {link[1]}")
            print(f"URL: {link[2]}")
            print("-" * 30)

        return links

    except Exception as e:
        logger.error(f"{supplier_name} için ürün linkleri çekilirken hata: {e}")
        return []

def analyze_page_structure(url: str, site_name: str):
    """
    Sayfa yapısını analiz eder ve fiyat elementlerini bulur
    """
    print(f"\n🔍 {site_name} sayfa yapısı analiz ediliyor...")
    print(f"URL: {url}")

    chrome_options = Options()
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument('--start-maximized')

    browser = None
    try:
        browser = webdriver.Chrome(options=chrome_options)
        browser.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        print("📄 Sayfa yükleniyor...")
        browser.get(url)
        time.sleep(5)

        html = browser.page_source
        soup = BeautifulSoup(html, 'html.parser')

        # Fiyat içerebilecek elementleri ara
        price_candidates = []

        # Fiyat kelimesi içeren elementler
        price_keywords = ['price', 'fiyat', 'tutar', 'amount', 'cost']
        for keyword in price_keywords:
            elements = soup.find_all(attrs={'class': re.compile(keyword, re.I)})
            elements += soup.find_all(attrs={'id': re.compile(keyword, re.I)})
            for elem in elements:
                text = elem.get_text(strip=True)
                if text and re.search(r'\d+[.,]\d+|\d+', text):
                    price_candidates.append({
                        'selector': f".{elem.get('class', [''])[0]}" if elem.get('class') else f"#{elem.get('id', '')}",
                        'text': text[:100],
                        'tag': elem.name
                    })

        # TL, ₺ sembolü içeren elementler
        currency_elements = soup.find_all(string=re.compile(r'[₺TL]|\d+[.,]\d+'))
        for elem in currency_elements:
            parent = elem.parent
            if parent:
                text = parent.get_text(strip=True)
                if re.search(r'\d+[.,]\d+', text):
                    classes = parent.get('class', [])
                    class_str = '.'.join(classes) if classes else ''
                    price_candidates.append({
                        'selector': f".{class_str}" if class_str else parent.name,
                        'text': text[:100],
                        'tag': parent.name
                    })

        # Sonuçları göster
        print(f"\n📋 {site_name} için bulunan fiyat adayları:")
        unique_candidates = []
        seen_texts = set()

        for candidate in price_candidates:
            if candidate['text'] not in seen_texts:
                seen_texts.add(candidate['text'])
                unique_candidates.append(candidate)

        for i, candidate in enumerate(unique_candidates[:10], 1):  # İlk 10 adayı göster
            print(f"{i}. Selector: {candidate['selector']}")
            print(f"   Text: {candidate['text']}")
            print(f"   Tag: {candidate['tag']}")
            print("-" * 50)

        if not unique_candidates:
            print("❌ Fiyat adayı bulunamadı")

            # Sayfa başlığını ve genel yapıyı kontrol et
            title = soup.find('title')
            print(f"📄 Sayfa başlığı: {title.get_text() if title else 'Bulunamadı'}")

            # Tüm class'ları listele
            all_classes = set()
            for elem in soup.find_all(class_=True):
                all_classes.update(elem['class'])

            price_related_classes = [cls for cls in all_classes if any(keyword in cls.lower() for keyword in ['price', 'fiyat', 'tutar', 'amount'])]
            if price_related_classes:
                print(f"💡 Fiyat ile ilgili class'lar: {price_related_classes[:10]}")

        return unique_candidates

    except Exception as e:
        logger.error(f"Sayfa analizi sırasında hata: {e}")
        print(f"❌ Hata: {e}")
        return []

    finally:
        if browser:
            browser.quit()

def main():
    """Ana fonksiyon"""
    print("=== Veritabanından Ürün Linkleri ===\n")

    # Tüm ürün linklerini çek
    print("1. Tüm ürün linkleri çekiliyor...")
    all_links = get_product_links()

    # Tüm linkler için fiyat çekme testi
    if all_links:
        print(f"\n🔍 Tüm linkler için fiyat çekme testi başlatılıyor...")
        print(f"Toplam {len(all_links)} link test edilecek\n")

        # Chrome seçenekleri
        chrome_options = Options()
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        chrome_options.add_argument('--start-maximized')
        # chrome_options.add_argument('--headless')  # Arka planda çalıştırmak için

        browser = None
        successful_extractions = 0
        failed_extractions = 0

        try:
            browser = webdriver.Chrome(options=chrome_options)
            browser.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            for i, link in enumerate(all_links, 1):
                url = link['supplier_product_url']
                product_name = link['product_name'][:50] + "..." if len(link['product_name']) > 50 else link['product_name']

                print(f"[{i}/{len(all_links)}] Test ediliyor: {product_name}")
                print(f"URL: {url}")

                try:
                    print("📄 Sayfa yükleniyor...")
                    browser.get(url)
                    time.sleep(3)  # Sayfanın yüklenmesini bekle

                    html = browser.page_source
                    price = extract_price(html, url)

                    if price > 0:
                        print(f"✅ Fiyat bulundu: {price} TL")
                        successful_extractions += 1

                        # Veritabanını güncelle
                        from database import Database
                        db = Database()
                        db.update_supplier_price(link['id'], price)
                        print(f"💾 Veritabanı güncellendi (ID: {link['id']})")

                    else:
                        print("❌ Fiyat çıkarılamadı")
                        failed_extractions += 1

                except Exception as e:
                    logger.error(f"Link işlenirken hata: {e}")
                    print(f"❌ Hata: {e}")
                    failed_extractions += 1

                print("-" * 80)

                # Siteler arası nezaket bekleme süresi
                if i < len(all_links):
                    print("⏳ Sonraki link için bekleniyor...")
                    time.sleep(2)

        except Exception as e:
            logger.error(f"Browser başlatılırken hata: {e}")
            print(f"❌ Browser hatası: {e}")

        finally:
            if browser:
                browser.quit()
                print("🔒 Browser kapatıldı.")

        # Özet rapor
        print(f"\n📊 ÖZET RAPOR:")
        print(f"✅ Başarılı: {successful_extractions}")
        print(f"❌ Başarısız: {failed_extractions}")
        print(f"📈 Başarı oranı: {(successful_extractions/len(all_links)*100):.1f}%")

    


    print(f"\nToplam {len(all_links)} ürün linki bulundu.\n")

    # Güncellenmiş fiyat çekme sistemini test et
    print("🧪 Güncellenmiş fiyat çekme sistemini test ediliyor...\n")

    test_sites = [
        {
            'name': 'MediaMarkt',
            'url': 'https://www.mediamarkt.com.tr/tr/product/_philips-mg372015-7si-1-arada-erkek-bakim-seti-1180484.html'
        },
        {
            'name': 'Vatan Bilgisayar',
            'url': 'https://www.vatanbilgisayar.com/philips-mg3720-15-3000-serisi-7-si1-arada-erkek-bakim-kiti.html'
        },
        {
            'name': 'Teknosa',
            'url': 'https://www.teknosa.com/philips-mg372015-7-si-1-arada-erkek-bakim-seti-p-120240912'
        }
    ]

    # Chrome seçenekleri
    chrome_options = Options()
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument('--start-maximized')

    browser = None
    try:
        browser = webdriver.Chrome(options=chrome_options)
        browser.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        for site in test_sites:
            print(f"🔍 {site['name']} test ediliyor...")
            print(f"URL: {site['url']}")

            try:
                browser.get(site['url'])
                time.sleep(4)

                html = browser.page_source
                price = extract_price(html, site['url'])

                if price > 0:
                    print(f"✅ Fiyat başarıyla çıkarıldı: {price} TL")
                else:
                    print(f"❌ Fiyat çıkarılamadı")

            except Exception as e:
                print(f"❌ Hata: {e}")

            print("-" * 60)
            time.sleep(2)

    except Exception as e:
        print(f"❌ Browser hatası: {e}")

    finally:
        if browser:
            browser.quit()
            print("🔒 Browser kapatıldı.")

if __name__ == "__main__":
    main()
