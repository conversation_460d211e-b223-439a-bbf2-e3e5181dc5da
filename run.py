#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Veritabanından ürün linklerini çeken script
"""

import logging
from database import Database
from typing import List, Dict

# Logging konfigürasyonu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_product_links() -> List[Dict]:
    """
    Veritabanından tüm ürün linklerini çeker

    Returns:
        List[Dict]: Ürün bilgileri ve linkleri içeren liste
    """
    try:
        db = Database()
        product_suppliers = db.get_all_product_suppliers()

        logger.info(f"Toplam {len(product_suppliers)} ürün linki bulundu")

        # Sonuçları yazdır
        for item in product_suppliers:
            print(f"ID: {item['id']}")
            print(f"Ürün ID: {item['product_id']}")
            print(f"Ürün Adı: {item['product_name']}")
            print(f"SKU: {item['sku']}")
            print(f"Supplier URL: {item['supplier_product_url']}")
            print(f"Mevcut Fiyat: {item['supplier_price']}")
            print("-" * 50)

        return product_suppliers

    except Exception as e:
        logger.error(f"Ürün linkleri çekilirken hata oluştu: {e}")
        return []

def get_product_links_by_supplier(supplier_name: str) -> List[Dict]:
    """
    Belirli bir tedarikçiye ait ürün linklerini çeker

    Args:
        supplier_name (str): Tedarikçi adı

    Returns:
        List[Dict]: Tedarikçiye ait ürün linkleri
    """
    try:
        from scrapers.base_scraper import BaseScraper

        # Geçici bir scraper instance oluştur
        scraper = BaseScraper(supplier_name)
        links = scraper.get_product_supplier_links()

        logger.info(f"{supplier_name} için {len(links)} ürün linki bulundu")

        # Sonuçları yazdır
        for link in links:
            print(f"PS ID: {link[0]}")
            print(f"Ürün ID: {link[1]}")
            print(f"URL: {link[2]}")
            print("-" * 30)

        return links

    except Exception as e:
        logger.error(f"{supplier_name} için ürün linkleri çekilirken hata: {e}")
        return []

def main():
    """Ana fonksiyon"""
    print("=== Veritabanından Ürün Linkleri ===\n")

    # Tüm ürün linklerini çek
    print("1. Tüm ürün linkleri çekiliyor...")
    all_links = get_product_links()

    for link in all_links:
       url = link['supplier_product_url']
       

    print(f"\nToplam {len(all_links)} ürün linki bulundu.\n")

    # İsteğe bağlı: Belirli bir tedarikçinin linklerini çek
    # supplier_name = "Hepsiburada"  # Örnek tedarikçi adı
    # print(f"2. {supplier_name} ürün linkleri çekiliyor...")
    # supplier_links = get_product_links_by_supplier(supplier_name)

if __name__ == "__main__":
    main()
