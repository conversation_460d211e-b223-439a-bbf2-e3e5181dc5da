import sqlite3
import requests
from bs4 import BeautifulSoup
from datetime import datetime
from abc import ABC, abstractmethod
import logging
import time
from typing import Optional, Dict, Any
import re

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

class BaseScraper(ABC):
    def __init__(self, supplier_name: str, db_path: str = "pazaryeri.db"):
        self.supplier_name = supplier_name
        self.db_path = db_path
        self.logger = logging.getLogger(f"{self.__class__.__name__}")
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'tr-TR,tr;q=0.9,en-US;q=0.8,en;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0'
        })
        
    def get_db_connection(self):
        return sqlite3.connect(self.db_path)
    
    def get_supplier_id(self) -> Optional[int]:
        conn = self.get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT id FROM suppliers WHERE name = ? AND is_deleted = 0", (self.supplier_name,))
        result = cursor.fetchone()
        conn.close()
        return result[0] if result else None
    
    def get_product_supplier_links(self) -> list:
        supplier_id = self.get_supplier_id()
        if not supplier_id:
            self.logger.error(f"Supplier '{self.supplier_name}' not found")
            return []
        
        conn = self.get_db_connection()
        cursor = conn.cursor()
        cursor.execute("""
            SELECT ps.id, ps.product_id, ps.supplier_product_url 
            FROM product_suppliers ps
            WHERE ps.supplier_id = ? AND ps.is_deleted = 0
        """, (supplier_id,))
        results = cursor.fetchall()
        conn.close()
        return results
    
    def update_product_supplier_price(self, ps_id: int, price: float):
        conn = self.get_db_connection()
        cursor = conn.cursor()
        cursor.execute("""
            UPDATE product_suppliers 
            SET supplier_price = ?, last_checked = ? 
            WHERE id = ?
        """, (price, datetime.now(), ps_id))
        conn.commit()
        conn.close()
        self.logger.info(f"Updated price for product_supplier {ps_id}: {price}")
    
    def update_product_prices(self, product_id: int):
        conn = self.get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT supplier_price 
            FROM product_suppliers 
            WHERE product_id = ? 
            AND is_deleted = 0 
            AND supplier_price IS NOT NULL 
            AND supplier_price > 0
        """, (product_id,))
        
        prices = [row[0] for row in cursor.fetchall()]
        
        if prices:
            min_price = min(prices)
            max_price = max(prices)
            avg_price = sum(prices) / len(prices)
            
            cursor.execute("""
                UPDATE products 
                SET min_price = ?, max_price = ?, avg_price = ?, price_updated_at = ? 
                WHERE id = ?
            """, (min_price, max_price, avg_price, datetime.now(), product_id))
            conn.commit()
            self.logger.info(f"Updated product {product_id} prices - Min: {min_price}, Max: {max_price}, Avg: {avg_price:.2f}")
        else:
            self.logger.warning(f"No valid prices found for product {product_id}")
        
        conn.close()
    
    def fetch_page(self, url: str) -> Optional[BeautifulSoup]:
        try:
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            return BeautifulSoup(response.content, 'html.parser')
        except Exception as e:
            self.logger.error(f"Error fetching {url}: {str(e)}")
            return None
    
    def clean_price(self, price_text: str) -> Optional[float]:
        try:
            price_text = re.sub(r'[^\d,.]', '', price_text)
            price_text = price_text.replace('.', '').replace(',', '.')
            return float(price_text)
        except:
            return None
    
    @abstractmethod
    def extract_price(self, soup: BeautifulSoup, url: str) -> Optional[float]:
        pass
    
    def scrape_all_products(self):
        links = self.get_product_supplier_links()
        self.logger.info(f"Found {len(links)} product links for {self.supplier_name}")
        
        processed_products = set()
        
        for ps_id, product_id, url in links:
            self.logger.info(f"Processing: {url}")
            
            soup = self.fetch_page(url)
            if not soup:
                continue
            
            price = self.extract_price(soup, url)
            if price and price > 0:
                self.update_product_supplier_price(ps_id, price)
                processed_products.add(product_id)
            else:
                self.logger.warning(f"Could not extract valid price from {url}")
            
            time.sleep(1)
        
        for product_id in processed_products:
            self.update_product_prices(product_id)
        
        self.logger.info(f"Completed processing {len(processed_products)} products for {self.supplier_name}")