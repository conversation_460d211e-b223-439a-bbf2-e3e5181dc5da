from .base_scraper import BaseScraper
from bs4 import BeautifulSoup
from typing import Optional
import re

class AmazonTRScraper(BaseScraper):
    def __init__(self, db_path: str = "pazaryeri.db"):
        super().__init__("Amazon TR", db_path)
    
    def extract_price(self, soup: BeautifulSoup, url: str) -> Optional[float]:
        try:
            price_elem = soup.find('span', class_='a-price-whole')
            if price_elem:
                price_text = price_elem.text.strip()
                return self.clean_price(price_text)
            
            price_elem = soup.find('span', {'class': 'a-price-range'})
            if price_elem:
                price_span = price_elem.find('span', class_='a-price')
                if price_span:
                    price_text = price_span.text.strip()
                    return self.clean_price(price_text)
            
            price_elem = soup.find('span', class_='a-price')
            if price_elem:
                price_text = price_elem.text.strip()
                return self.clean_price(price_text)
            
            price_elem = soup.find('span', {'id': 'priceblock_dealprice'})
            if price_elem:
                price_text = price_elem.text.strip()
                return self.clean_price(price_text)
            
            price_elem = soup.find('span', {'id': 'priceblock_ourprice'})
            if price_elem:
                price_text = price_elem.text.strip()
                return self.clean_price(price_text)
            
        except Exception as e:
            self.logger.error(f"Error extracting price from {url}: {str(e)}")
        
        return None

if __name__ == "__main__":
    scraper = AmazonTRScraper()
    scraper.scrape_all_products()