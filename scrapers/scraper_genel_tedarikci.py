from .base_scraper import BaseScraper
from bs4 import BeautifulSoup
from typing import Optional
import json
import re

class GenelTedarikciScraper(BaseScraper):
    def __init__(self, db_path: str = "pazaryeri.db"):
        super().__init__("Genel Tedarikçi", db_path)
    
    def extract_price(self, soup: BeautifulSoup, url: str) -> Optional[float]:
        try:
            script_tags = soup.find_all('script', type='application/ld+json')
            for script in script_tags:
                try:
                    data = json.loads(script.string)
                    if isinstance(data, dict) and data.get('@type') == 'Product':
                        if 'offers' in data:
                            price = data['offers'].get('price')
                            if price:
                                return float(price)
                except:
                    continue
            
            price_patterns = [
                {'tag': 'span', 'attrs': {'class': re.compile(r'price')}},
                {'tag': 'div', 'attrs': {'class': re.compile(r'price')}},
                {'tag': 'p', 'attrs': {'class': re.compile(r'price')}},
                {'tag': 'meta', 'attrs': {'property': 'product:price:amount'}},
                {'tag': 'meta', 'attrs': {'name': 'price'}},
                {'tag': 'span', 'attrs': {'itemprop': 'price'}},
            ]
            
            for pattern in price_patterns:
                elem = soup.find(pattern['tag'], pattern['attrs'])
                if elem:
                    if pattern['tag'] == 'meta':
                        content = elem.get('content')
                        if content:
                            try:
                                return float(content)
                            except:
                                return self.clean_price(content)
                    else:
                        price_text = elem.text.strip()
                        price = self.clean_price(price_text)
                        if price:
                            return price
            
        except Exception as e:
            self.logger.error(f"Error extracting price from {url}: {str(e)}")
        
        return None

if __name__ == "__main__":
    scraper = GenelTedarikciScraper()
    scraper.scrape_all_products()