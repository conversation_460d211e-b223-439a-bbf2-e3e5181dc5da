from .base_scraper import BaseScraper
from bs4 import BeautifulSoup
from typing import Optional
import json
import re

class MediaMarktScraper(BaseScraper):
    def __init__(self, db_path: str = "pazaryeri.db"):
        super().__init__("MediaMarkt", db_path)
    
    def extract_price(self, soup: BeautifulSoup, url: str) -> Optional[float]:
        try:
            script_tags = soup.find_all('script', type='application/ld+json')
            for script in script_tags:
                try:
                    data = json.loads(script.string)
                    if isinstance(data, dict) and data.get('@type') == 'Product':
                        if 'offers' in data:
                            price = data['offers'].get('price')
                            if price:
                                return float(price)
                except:
                    continue
            
            for script in soup.find_all('script'):
                if script.string and 'price' in script.string.lower():
                    prices = re.findall(r'"price":\s*"?(\d+\.?\d*)"?', script.string)
                    if prices:
                        try:
                            return float(prices[0])
                        except:
                            pass
            
            price_elem = soup.find('div', class_='price')
            if price_elem:
                price_text = price_elem.text.strip()
                return self.clean_price(price_text)
            
            price_elem = soup.find('span', {'class': re.compile(r'price.*big')})
            if price_elem:
                price_text = price_elem.text.strip()
                return self.clean_price(price_text)
            
            price_elem = soup.find('meta', {'property': 'product:price:amount'})
            if price_elem and price_elem.get('content'):
                try:
                    return float(price_elem['content'])
                except:
                    pass
            
        except Exception as e:
            self.logger.error(f"Error extracting price from {url}: {str(e)}")
        
        return None

if __name__ == "__main__":
    scraper = MediaMarktScraper()
    scraper.scrape_all_products()