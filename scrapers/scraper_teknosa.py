from .base_scraper import BaseScraper
from bs4 import BeautifulSoup
from typing import Optional
import re

class TeknosaScraper(BaseScraper):
    def __init__(self, db_path: str = "pazaryeri.db"):
        super().__init__("Teknosa", db_path)
    
    def extract_price(self, soup: BeautifulSoup, url: str) -> Optional[float]:
        try:
            price_elem = soup.find('span', class_='prc')
            if price_elem:
                price_text = price_elem.text.strip()
                return self.clean_price(price_text)
            
            for script in soup.find_all('script'):
                if script.string:
                    prices = re.findall(r'"price":\s*["\']?(\d+\.?\d*)["\']?', script.string)
                    if prices:
                        try:
                            return float(prices[0])
                        except:
                            pass
            
            price_elem = soup.find('div', class_='pdp-price')
            if price_elem:
                price_span = price_elem.find('span')
                if price_span:
                    price_text = price_span.text.strip()
                    return self.clean_price(price_text)
            
            price_elem = soup.find('span', {'class': re.compile(r'price.*main')})
            if price_elem:
                price_text = price_elem.text.strip()
                return self.clean_price(price_text)
            
            price_elem = soup.find('div', {'class': 'product-price'})
            if price_elem:
                price_text = price_elem.text.strip()
                return self.clean_price(price_text)
            
            price_elem = soup.find('meta', {'property': 'product:price:amount'})
            if price_elem and price_elem.get('content'):
                try:
                    return float(price_elem['content'])
                except:
                    pass
            
        except Exception as e:
            self.logger.error(f"Error extracting price from {url}: {str(e)}")
        
        return None

if __name__ == "__main__":
    scraper = TeknosaScraper()
    scraper.scrape_all_products()