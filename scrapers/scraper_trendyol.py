from .base_scraper import BaseScraper
from bs4 import BeautifulSoup
from typing import Optional
import json

class TrendyolScraper(BaseScraper):
    def __init__(self, db_path: str = "pazaryeri.db"):
        super().__init__("Trendyol", db_path)
    
    def extract_price(self, soup: BeautifulSoup, url: str) -> Optional[float]:
        try:
            script_tags = soup.find_all('script', type='application/ld+json')
            for script in script_tags:
                try:
                    data = json.loads(script.string)
                    if isinstance(data, dict) and data.get('@type') == 'Product':
                        if 'offers' in data:
                            price = data['offers'].get('price')
                            if price:
                                return float(price)
                except:
                    continue
            
            price_elem = soup.find('span', class_='prc-dsc')
            if price_elem:
                price_text = price_elem.text.strip()
                return self.clean_price(price_text)
            
            price_elem = soup.find('span', class_='prc-slg')
            if price_elem:
                price_text = price_elem.text.strip()
                return self.clean_price(price_text)
            
            meta_price = soup.find('meta', {'name': 'twitter:data1'})
            if meta_price and meta_price.get('content'):
                price_text = meta_price['content']
                return self.clean_price(price_text)
            
        except Exception as e:
            self.logger.error(f"Error extracting price from {url}: {str(e)}")
        
        return None

if __name__ == "__main__":
    scraper = TrendyolScraper()
    scraper.scrape_all_products()