from .base_scraper import BaseScraper
from bs4 import BeautifulSoup
from typing import Optional
import re

class VatanBilgisayarScraper(BaseScraper):
    def __init__(self, db_path: str = "pazaryeri.db"):
        super().__init__("Vatan Bilgisayar", db_path)
    
    def extract_price(self, soup: BeautifulSoup, url: str) -> Optional[float]:
        try:
            price_elem = soup.find('span', class_='product-price__price')
            if price_elem:
                price_text = price_elem.text.strip()
                return self.clean_price(price_text)
            
            price_elem = soup.find('div', class_='product-price')
            if price_elem:
                price_span = price_elem.find('span')
                if price_span:
                    price_text = price_span.text.strip()
                    return self.clean_price(price_text)
            
            price_elem = soup.find('span', {'class': re.compile(r'price.*current')})
            if price_elem:
                price_text = price_elem.text.strip()
                return self.clean_price(price_text)
            
            meta_price = soup.find('meta', {'property': 'product:price:amount'})
            if meta_price and meta_price.get('content'):
                try:
                    return float(meta_price['content'])
                except:
                    pass
            
        except Exception as e:
            self.logger.error(f"Error extracting price from {url}: {str(e)}")
        
        return None

if __name__ == "__main__":
    scraper = VatanBilgisayarScraper()
    scraper.scrape_all_products()