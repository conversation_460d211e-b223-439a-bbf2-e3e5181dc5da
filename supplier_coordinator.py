"""
Supplier Coordinator - Tüm supplier servislerini koordine eden ana servis
"""

import asyncio
import logging
import importlib
from typing import List, Dict, Any, Optional
from datetime import datetime
import database_operations as db

# Logging konfigürasyonu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('supplier_coordinator.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SupplierCoordinator:
    """
    Tüm supplier servislerini koordine eden ana class.
    <PERSON><PERSON><PERSON>ış<PERSON>, hata yönetimi ve raporlama sağlar.
    """
    
    def __init__(self):
        self.supplier_services = {
            'Hepsiburada': 'HepsiBurada_PriceCheck.HepsiburadaService',
            'Trendyol': 'Trendyol_PriceCheck.TrendyolService',
            'Teknosa': 'Teknosa_PriceCheck.TeknosaService',
            'MediaMarkt': 'MediaMarkt_PriceCheck.MediaMarktService',
            'Vatan Bilgisayar': 'VatanBilgisayar_PriceCheck.VatanBilgisayarService',
            'Amazon TR': 'AmazonTR_PriceCheck.AmazonTRService',
            'N11': 'N11_PriceCheck.N11Service'
        }
        self.results = {}
        
    def get_active_suppliers(self) -> List[str]:
        """Veritabanından aktif supplier'ları çeker"""
        try:
            conn = db.get_db_connection()
            cursor = conn.cursor()
            cursor.execute("""
                SELECT DISTINCT s.name 
                FROM suppliers s 
                JOIN product_suppliers ps ON s.id = ps.supplier_id 
                WHERE s.is_deleted = 0 AND ps.is_deleted = 0
            """)
            suppliers = [row[0] for row in cursor.fetchall()]
            conn.close()
            return suppliers
        except Exception as e:
            logger.error(f"Aktif supplier'lar çekilirken hata: {e}")
            return []
    
    async def run_supplier_service(self, supplier_name: str) -> Dict[str, Any]:
        """Tek bir supplier servisi çalıştırır"""
        start_time = datetime.now()
        result = {
            'supplier': supplier_name,
            'status': 'failed',
            'start_time': start_time,
            'end_time': None,
            'duration': None,
            'products_processed': 0,
            'products_updated': 0,
            'errors': []
        }
        
        try:
            if supplier_name not in self.supplier_services:
                result['errors'].append(f"Supplier servisi bulunamadı: {supplier_name}")
                return result
            
            # Supplier servisini dinamik olarak import et
            module_class = self.supplier_services[supplier_name]
            module_name, class_name = module_class.rsplit('.', 1)
            
            try:
                module = importlib.import_module(module_name)
                service_class = getattr(module, class_name)
            except (ImportError, AttributeError) as e:
                result['errors'].append(f"Servis import hatası: {e}")
                return result
            
            # Servisi çalıştır
            logger.info(f"{supplier_name} servisi başlatılıyor...")
            
            async with service_class() as service:
                # Ürünleri al
                products = db.get_products_for_supplier(supplier_name)
                result['products_processed'] = len(products)
                
                if not products:
                    result['status'] = 'no_products'
                    logger.info(f"{supplier_name} için ürün bulunamadı")
                    return result
                
                # Her ürün için fiyat güncelle
                updated_count = 0
                for product in products:
                    try:
                        price = await service.extract_price(product['supplier_product_url'])
                        if price is not None:
                            await service.update_product_price(product['product_supplier_id'], price)
                            updated_count += 1
                        
                        # Rate limiting
                        await asyncio.sleep(1)
                        
                    except Exception as e:
                        error_msg = f"Ürün işleme hatası - ID: {product['product_id']}, Hata: {str(e)}"
                        result['errors'].append(error_msg)
                        logger.error(error_msg)
                
                result['products_updated'] = updated_count
                result['status'] = 'success'
                logger.info(f"{supplier_name} servisi tamamlandı. {updated_count}/{len(products)} ürün güncellendi")
                
        except Exception as e:
            error_msg = f"{supplier_name} servisi genel hatası: {str(e)}"
            result['errors'].append(error_msg)
            logger.error(error_msg)
        
        finally:
            result['end_time'] = datetime.now()
            result['duration'] = (result['end_time'] - result['start_time']).total_seconds()
        
        return result
    
    async def run_all_suppliers_parallel(self, max_concurrent: int = 3) -> Dict[str, Any]:
        """Tüm supplier'ları paralel olarak çalıştırır"""
        logger.info("Tüm supplier servisleri paralel olarak başlatılıyor...")
        
        active_suppliers = self.get_active_suppliers()
        available_suppliers = [s for s in active_suppliers if s in self.supplier_services]
        
        if not available_suppliers:
            logger.warning("Çalıştırılabilir supplier servisi bulunamadı")
            return {'status': 'no_suppliers', 'results': {}}
        
        logger.info(f"Çalıştırılacak supplier'lar: {available_suppliers}")
        
        # Semaphore ile eş zamanlı çalışan servis sayısını sınırla
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def run_with_semaphore(supplier_name):
            async with semaphore:
                return await self.run_supplier_service(supplier_name)
        
        # Tüm servisleri paralel çalıştır
        tasks = [run_with_semaphore(supplier) for supplier in available_suppliers]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Sonuçları organize et
        final_results = {}
        for i, result in enumerate(results):
            supplier_name = available_suppliers[i]
            if isinstance(result, Exception):
                final_results[supplier_name] = {
                    'supplier': supplier_name,
                    'status': 'exception',
                    'error': str(result)
                }
            else:
                final_results[supplier_name] = result
        
        return {
            'status': 'completed',
            'total_suppliers': len(available_suppliers),
            'results': final_results
        }
    
    async def run_single_supplier(self, supplier_name: str) -> Dict[str, Any]:
        """Tek bir supplier'ı çalıştırır"""
        logger.info(f"Tek supplier çalıştırılıyor: {supplier_name}")
        
        if supplier_name not in self.supplier_services:
            return {
                'status': 'error',
                'message': f"Supplier servisi bulunamadı: {supplier_name}"
            }
        
        result = await self.run_supplier_service(supplier_name)
        return {
            'status': 'completed',
            'results': {supplier_name: result}
        }
    
    def generate_report(self, results: Dict[str, Any]) -> str:
        """Çalışma sonuçlarının raporunu oluşturur"""
        if results['status'] == 'no_suppliers':
            return "Çalıştırılabilir supplier servisi bulunamadı."
        
        report_lines = [
            "=" * 60,
            "SUPPLIER SERVİSLERİ ÇALIŞMA RAPORU",
            "=" * 60,
            f"Toplam Supplier: {results.get('total_suppliers', 0)}",
            f"Çalışma Zamanı: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            ""
        ]
        
        for supplier_name, result in results.get('results', {}).items():
            report_lines.extend([
                f"Supplier: {supplier_name}",
                f"  Durum: {result.get('status', 'unknown')}",
                f"  İşlenen Ürün: {result.get('products_processed', 0)}",
                f"  Güncellenen Ürün: {result.get('products_updated', 0)}",
                f"  Süre: {result.get('duration', 0):.2f} saniye"
            ])
            
            if result.get('errors'):
                report_lines.append(f"  Hatalar: {len(result['errors'])}")
                for error in result['errors'][:3]:  # İlk 3 hatayı göster
                    report_lines.append(f"    - {error}")
                if len(result['errors']) > 3:
                    report_lines.append(f"    ... ve {len(result['errors']) - 3} hata daha")
            
            report_lines.append("")
        
        return "\n".join(report_lines)

async def main():
    """Ana fonksiyon - tüm supplier'ları çalıştır"""
    coordinator = SupplierCoordinator()
    
    # Tüm supplier'ları paralel çalıştır
    results = await coordinator.run_all_suppliers_parallel(max_concurrent=2)
    
    # Rapor oluştur ve göster
    report = coordinator.generate_report(results)
    print(report)
    
    # Raporu dosyaya kaydet
    with open(f'supplier_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.txt', 'w', encoding='utf-8') as f:
        f.write(report)

if __name__ == '__main__':
    asyncio.run(main())
