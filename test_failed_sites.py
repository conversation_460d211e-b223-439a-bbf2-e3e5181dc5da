#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Başarısız siteleri test eden script
"""

import logging
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from bs4 import BeautifulSoup
import re

# Logging konfigürasyonu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def extract_price(html: str, url: str = "") -> float:
    """
    HTML içeriğinden fiyat bilgisini çıkarır
    """
    try:
        soup = BeautifulSoup(html, 'html.parser')
        
        # Çok uzun metinleri filtrele
        if len(html) > 500000:  # 500KB'dan büyükse
            logger.warning(f"Çok büyük HTML içeriği: {len(html)} karakter")
        
        # Özel durumlar için fiyat çıkarma (Trendyol gibi)
        special_patterns = [
            r'son \d+ günün en düşük fiyatı[!]*(\d{1,4})\s*tl',  # "Son 14 Günün En Düşük Fiyatı!935 TL"
            r'en düşük fiyat[!]*(\d{1,4})\s*tl'  # "En Düşük Fiyat!935 TL"
        ]
        
        for pattern in special_patterns:
            match = re.search(pattern, html.lower())
            if match:
                try:
                    price_value = float(match.group(1))
                    if 10 <= price_value <= 100000:
                        logger.info(f"Özel pattern ile çıkarılan fiyat: {price_value}")
                        return price_value
                except ValueError:
                    continue
        
        # Normal fiyat arama
        price_patterns = [
            r'(\d{1,3}(?:[.,]\d{3})*(?:[.,]\d{1,2})?)\s*[₺TL]',  # 1.234,56 TL veya ₺1.234,56
            r'[₺TL]\s*(\d{1,3}(?:[.,]\d{3})*(?:[.,]\d{1,2})?)',  # TL 1.234,56 veya ₺ 1.234,56
            r'(\d{1,3}(?:[.,]\d{3})*(?:[.,]\d{1,2})?)\s*(?:TL|₺|lira)',  # 1.234,56 TL
            r'(\d{1,3}(?:\.\d{3})*(?:,\d{1,2})?)\s*(?=\s|$|,)',  # Sadece sayı (Türk formatı) - virgül ile biten
            r'(\d{1,3}(?:,\d{3})*(?:\.\d{1,2})?)\s*(?=\s|$)',   # Sadece sayı (US formatı)
            r'(\d{1,4}),?\s*(?=\s|$)',  # Amazon formatı: "899," veya "1138,"
            r'(\d{1,4})\s*(?=\s|$)'     # Basit sayı formatı
        ]
        
        price_value = None
        for pattern in price_patterns:
            matches = re.findall(pattern, html, re.IGNORECASE)
            if matches:
                for match in matches:
                    try:
                        # Türk formatını normalize et
                        clean_price = match.replace('.', '').replace(',', '.')
                        if clean_price.count('.') > 1:
                            # Birden fazla nokta varsa, son nokta ondalık ayırıcı
                            parts = clean_price.split('.')
                            clean_price = ''.join(parts[:-1]) + '.' + parts[-1]
                        
                        value = float(clean_price)
                        
                        # Makul fiyat aralığı kontrolü (10 TL - 100.000 TL)
                        if 10 <= value <= 100000:
                            if price_value is None or value > price_value:
                                price_value = value
                    except ValueError:
                        continue
                
                if price_value:
                    break
        
        if price_value:
            logger.info(f"Çıkarılan fiyat: {price_value}")
            return price_value
        else:
            logger.warning(f"Geçerli fiyat bulunamadı")
            return 0.0
        
    except Exception as e:
        logger.error(f"Fiyat çıkarılırken hata: {e}")
        return 0.0

def test_failed_sites():
    """Başarısız siteleri test et"""
    
    test_urls = [
        {
            'name': 'Trendyol',
            'url': 'https://www.trendyol.com/philips/7-si-1-arada-erkek-bakim-seti-mg3720-15-yuz-sac-islak-kuru-kullanim-p-3593571?boutiqueId=61&merchantId=4513'
        },
        {
            'name': 'Amazon 1',
            'url': 'https://www.amazon.com.tr/Philips-MG3720-Erkek-Bak%25C4%25B1m-Arada/dp/B075JNQK46?th=1'
        },
        {
            'name': 'Amazon 2',
            'url': 'https://www.amazon.com.tr/Gamenote-Fuxi-H3-Gaming-Mikrofonlu-Kulakl%C4%B1%C4%9F%C4%B1/dp/B0CHJXCJTW'
        }
    ]
    
    # Chrome seçenekleri
    chrome_options = Options()
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument('--start-maximized')
    
    browser = None
    try:
        browser = webdriver.Chrome(options=chrome_options)
        browser.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        for site in test_urls:
            print(f"\n🔍 {site['name']} test ediliyor...")
            print(f"URL: {site['url']}")
            
            try:
                browser.get(site['url'])
                time.sleep(5)
                
                html = browser.page_source
                price = extract_price(html, site['url'])
                
                if price > 0:
                    print(f"✅ Fiyat başarıyla çıkarıldı: {price} TL")
                else:
                    print(f"❌ Fiyat çıkarılamadı")
                    
            except Exception as e:
                print(f"❌ Hata: {e}")
            
            print("-" * 60)
            time.sleep(2)
            
    except Exception as e:
        print(f"❌ Browser hatası: {e}")
        
    finally:
        if browser:
            browser.quit()
            print("🔒 Browser kapatıldı.")

if __name__ == "__main__":
    test_failed_sites()
