#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JBL ürünü için fiyat çekme testi
"""

import logging
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from bs4 import BeautifulSoup
import re

# Logging konfigürasyonu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def extract_price_jbl(html: str, url: str = "") -> float:
    """JBL için fiyat çıkarma"""
    try:
        soup = BeautifulSoup(html, 'html.parser')
        
        # Çok uzun metinleri filtrele
        if len(html) > 500000:
            logger.warning(f"Çok büyük HTML içeriği: {len(html)} karakter")
        
        # Site-spesifik selektörler
        site_selectors = {
            'hepsiburada.com': [
                '.price-current',
                '.price',
                '[data-test-id="price-current-price"]',
                '.product-price'
            ],
            'trendyol.com': [
                '.prc-dsc',
                '.prc-org', 
                '.price-current',
                '.product-price'
            ],
            'teknosa.com': [
                '.pds-prices',
                '.swogo-price-with-discount',
                '.swogo-price',
                '.product-price'
            ],
            'amazon.com.tr': [
                '.a-price-whole',
                '.a-price',
                '.price'
            ]
        }
        
        # Site tespiti
        site_key = None
        for site in site_selectors.keys():
            if site in url.lower():
                site_key = site
                break
        
        # Fiyat arama
        price_text = None
        if site_key:
            for selector in site_selectors[site_key]:
                elements = soup.select(selector)
                if elements:
                    for element in elements:
                        text = element.get_text(strip=True)
                        if text and re.search(r'\d', text) and len(text) <= 100:
                            if re.search(r'[₺TL]', text):
                                price_text = text
                                break
                    if price_text:
                        break
        
        # Genel fiyat arama
        if not price_text:
            general_selectors = [
                '.price', '.product-price', '.current-price', '.price-current',
                '[class*="price"]', '[class*="fiyat"]'
            ]
            
            for selector in general_selectors:
                elements = soup.select(selector)
                for element in elements:
                    text = element.get_text(strip=True)
                    if text and re.search(r'\d', text) and re.search(r'[₺TL]', text) and len(text) <= 50:
                        price_text = text
                        break
                if price_text:
                    break
        
        if price_text:
            # Fiyat çıkarma
            price_patterns = [
                r'(\d{1,3}(?:[.,]\d{3})*(?:[.,]\d{1,2})?)\s*[₺TL]',
                r'[₺TL]\s*(\d{1,3}(?:[.,]\d{3})*(?:[.,]\d{1,2})?)',
                r'(\d{1,3}(?:[.,]\d{3})*(?:[.,]\d{1,2})?)\s*(?:TL|₺|lira)',
                r'(\d{1,4}),?\s*(?=\s|$)',
                r'(\d{1,4})\s*(?=\s|$)'
            ]
            
            for pattern in price_patterns:
                matches = re.findall(pattern, price_text, re.IGNORECASE)
                if matches:
                    for match in matches:
                        try:
                            clean_price = match.replace('.', '').replace(',', '.')
                            if clean_price.count('.') > 1:
                                parts = clean_price.split('.')
                                clean_price = ''.join(parts[:-1]) + '.' + parts[-1]
                            
                            value = float(clean_price)
                            if 10 <= value <= 100000:
                                logger.info(f"JBL fiyat bulundu: {value} TL")
                                return value
                        except ValueError:
                            continue
        
        return 0.0
        
    except Exception as e:
        logger.error(f"JBL fiyat çıkarma hatası: {e}")
        return 0.0

def test_jbl_prices():
    """JBL fiyatlarını test et"""
    
    # Gerçek JBL URL'leri
    test_urls = [
        {
            'name': 'Hepsiburada',
            'url': 'https://www.hepsiburada.com/jbl-tune-720bt-kulak-ustu-kablosuz-bluetooth-kulaklik-siyah-p-HBV00001BQZQY'
        },
        {
            'name': 'Trendyol',
            'url': 'https://www.trendyol.com/jbl/tune-720bt-kulak-ustu-kablosuz-bluetooth-kulaklik-siyah-p-4567890'
        },
        {
            'name': 'Teknosa',
            'url': 'https://www.teknosa.com/jbl-tune-720bt-kulak-ustu-kablosuz-bluetooth-kulaklik-siyah'
        },
        {
            'name': 'Amazon',
            'url': 'https://www.amazon.com.tr/JBL-Tune-720BT-Kablosuz-Kulakl%C4%B1k/dp/B0B1234567'
        }
    ]
    
    # Chrome seçenekleri
    chrome_options = Options()
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument('--start-maximized')
    
    browser = None
    successful_sites = 0
    
    try:
        browser = webdriver.Chrome(options=chrome_options)
        browser.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        print("🎧 JBL Tune T720bt fiyat testi başlatılıyor...\n")
        
        for site in test_urls:
            print(f"🔍 {site['name']} test ediliyor...")
            print(f"URL: {site['url']}")
            
            try:
                browser.get(site['url'])
                time.sleep(5)
                
                html = browser.page_source
                price = extract_price_jbl(html, site['url'])
                
                if price > 0:
                    print(f"✅ Fiyat başarıyla çıkarıldı: {price} TL")
                    successful_sites += 1
                    
                    # Veritabanını güncelle
                    try:
                        import sqlite3
                        conn = sqlite3.connect('pazaryeri.db')
                        cursor = conn.cursor()
                        
                        # Product supplier ID'sini bul
                        cursor.execute("""
                            SELECT ps.id FROM product_suppliers ps
                            JOIN products p ON ps.product_id = p.id
                            JOIN suppliers s ON ps.supplier_id = s.id
                            WHERE p.sku = 'JBL-T720' AND s.name = ?
                        """, (site['name'],))
                        
                        result = cursor.fetchone()
                        if result:
                            ps_id = result[0]
                            cursor.execute("""
                                UPDATE product_suppliers 
                                SET supplier_price = ?, last_checked = CURRENT_TIMESTAMP
                                WHERE id = ?
                            """, (price, ps_id))
                            conn.commit()
                            print(f"💾 Veritabanı güncellendi (PS ID: {ps_id})")
                        
                        conn.close()
                        
                    except Exception as db_error:
                        print(f"❌ Veritabanı güncelleme hatası: {db_error}")
                        
                else:
                    print(f"❌ Fiyat çıkarılamadı")
                    
                    # Hata ayıklama için sayfa başlığını kontrol et
                    soup = BeautifulSoup(html, 'html.parser')
                    title = soup.find('title')
                    if title:
                        title_text = title.get_text()
                        if '404' in title_text or 'bulunamadı' in title_text.lower():
                            print(f"   ⚠️ Sayfa bulunamadı: {title_text}")
                        else:
                            print(f"   📄 Sayfa başlığı: {title_text[:50]}...")
                    
            except Exception as e:
                print(f"❌ Hata: {e}")
            
            print("-" * 60)
            time.sleep(3)
        
        print(f"\n📊 JBL Test Sonuçları:")
        print(f"✅ Başarılı: {successful_sites}/{len(test_urls)}")
        print(f"📈 Başarı oranı: {(successful_sites/len(test_urls)*100):.1f}%")
        
        # Fiyat istatistiklerini güncelle
        if successful_sites > 0:
            try:
                from database import Database
                db = Database()
                db.update_product_price_statistics(3)  # JBL product ID = 3
                print("📊 JBL fiyat istatistikleri güncellendi")
            except Exception as e:
                print(f"❌ Fiyat istatistikleri güncelleme hatası: {e}")
            
    except Exception as e:
        print(f"❌ Browser hatası: {e}")
        
    finally:
        if browser:
            browser.quit()
            print("🔒 Browser kapatıldı.")

if __name__ == "__main__":
    test_jbl_prices()
