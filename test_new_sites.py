#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Yeni eklenen siteleri test eden script
"""

import logging
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from bs4 import BeautifulSoup
import re

# Logging konfigürasyonu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def extract_price_new_sites(html: str, url: str = "") -> float:
    """Yeni siteler için fiyat çıkarma"""
    try:
        soup = BeautifulSoup(html, 'html.parser')
        
        # Site-spesifik selektörler
        site_selectors = {
            'n11.com': [
                '.newPrice',
                '.priceContainer .price',
                '.productPrice .price',
                '.ins',
                '[class*="price"]'
            ],
            'gittigidiyor.com': [
                '.price-current',
                '.gg-price',
                '.product-price',
                '.price'
            ],
            'ciceksepeti.com': [
                '.price',
                '.product-price',
                '.current-price',
                '[class*="price"]'
            ],
            'morhipo.com': [
                '.price',
                '.product-price',
                '.current-price',
                '[class*="price"]'
            ]
        }
        
        # Site tespiti
        site_key = None
        for site in site_selectors.keys():
            if site in url.lower():
                site_key = site
                break
        
        # Fiyat arama
        price_text = None
        if site_key:
            for selector in site_selectors[site_key]:
                elements = soup.select(selector)
                if elements:
                    # En uygun elementi seç
                    for element in elements:
                        text = element.get_text(strip=True)
                        if text and re.search(r'\d', text) and len(text) <= 100:
                            # TL veya ₺ içeriyorsa öncelik ver
                            if re.search(r'[₺TL]', text):
                                price_text = text
                                break
                    if price_text:
                        break
        
        # Genel fiyat arama
        if not price_text:
            general_selectors = [
                '.price', '.product-price', '.current-price', '.price-current',
                '[class*="price"]', '[class*="fiyat"]'
            ]
            
            for selector in general_selectors:
                elements = soup.select(selector)
                for element in elements:
                    text = element.get_text(strip=True)
                    if text and re.search(r'\d', text) and re.search(r'[₺TL]', text) and len(text) <= 50:
                        price_text = text
                        break
                if price_text:
                    break
        
        if price_text:
            # Fiyat çıkarma
            price_patterns = [
                r'(\d{1,3}(?:[.,]\d{3})*(?:[.,]\d{1,2})?)\s*[₺TL]',
                r'[₺TL]\s*(\d{1,3}(?:[.,]\d{3})*(?:[.,]\d{1,2})?)',
                r'(\d{1,3}(?:[.,]\d{3})*(?:[.,]\d{1,2})?)\s*(?:TL|₺|lira)',
                r'(\d{1,4}),?\s*(?=\s|$)',
                r'(\d{1,4})\s*(?=\s|$)'
            ]
            
            for pattern in price_patterns:
                matches = re.findall(pattern, price_text, re.IGNORECASE)
                if matches:
                    for match in matches:
                        try:
                            clean_price = match.replace('.', '').replace(',', '.')
                            if clean_price.count('.') > 1:
                                parts = clean_price.split('.')
                                clean_price = ''.join(parts[:-1]) + '.' + parts[-1]
                            
                            value = float(clean_price)
                            if 10 <= value <= 100000:
                                logger.info(f"Fiyat bulundu: {value} TL")
                                return value
                        except ValueError:
                            continue
        
        return 0.0
        
    except Exception as e:
        logger.error(f"Fiyat çıkarma hatası: {e}")
        return 0.0

def test_new_sites():
    """Yeni siteleri test et"""
    
    test_urls = [
        {
            'name': 'N11',
            'url': 'https://www.n11.com/urun/philips-mg3720-15-7-si-1-arada-erkek-bakim-seti-1180484'
        },
        {
            'name': 'GittiGidiyor',
            'url': 'https://www.gittigidiyor.com/kisisel-bakim/philips-mg3720-15-7-si-1-arada-erkek-bakim-seti'
        },
        {
            'name': 'ÇiçekSepeti',
            'url': 'https://www.ciceksepeti.com/philips-mg3720-15-erkek-bakim-seti'
        },
        {
            'name': 'Morhipo',
            'url': 'https://www.morhipo.com/philips-mg3720-15-erkek-bakim-seti'
        }
    ]
    
    # Chrome seçenekleri
    chrome_options = Options()
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument('--start-maximized')
    
    browser = None
    successful_sites = 0
    
    try:
        browser = webdriver.Chrome(options=chrome_options)
        browser.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        print("🧪 Yeni siteler test ediliyor...\n")
        
        for site in test_urls:
            print(f"🔍 {site['name']} test ediliyor...")
            print(f"URL: {site['url']}")
            
            try:
                browser.get(site['url'])
                time.sleep(5)
                
                html = browser.page_source
                price = extract_price_new_sites(html, site['url'])
                
                if price > 0:
                    print(f"✅ Fiyat başarıyla çıkarıldı: {price} TL")
                    successful_sites += 1
                else:
                    print(f"❌ Fiyat çıkarılamadı")
                    
                    # Hata ayıklama için sayfa başlığını kontrol et
                    soup = BeautifulSoup(html, 'html.parser')
                    title = soup.find('title')
                    if title:
                        title_text = title.get_text()
                        if '404' in title_text or 'bulunamadı' in title_text.lower():
                            print(f"   ⚠️ Sayfa bulunamadı: {title_text}")
                        else:
                            print(f"   📄 Sayfa başlığı: {title_text[:50]}...")
                    
            except Exception as e:
                print(f"❌ Hata: {e}")
            
            print("-" * 60)
            time.sleep(3)
        
        print(f"\n📊 Test Sonuçları:")
        print(f"✅ Başarılı: {successful_sites}/{len(test_urls)}")
        print(f"📈 Başarı oranı: {(successful_sites/len(test_urls)*100):.1f}%")
            
    except Exception as e:
        print(f"❌ Browser hatası: {e}")
        
    finally:
        if browser:
            browser.quit()
            print("🔒 Browser kapatıldı.")

if __name__ == "__main__":
    test_new_sites()
