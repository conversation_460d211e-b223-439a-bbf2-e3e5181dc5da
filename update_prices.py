import sys
import importlib
import logging
from datetime import datetime
import sqlite3

import os

# Create logs directory if not exists
os.makedirs('logs', exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'logs/price_update_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

SUPPLIER_SCRAPERS = {
    'Hepsiburada': 'scrapers.scraper_hepsiburada.HepsiburadaScraper',
    'Trendyol': 'scrapers.scraper_trendyol.TrendyolScraper',
    'Teknosa': 'scrapers.scraper_teknosa.TeknosaScraper',
    'MediaMarkt': 'scrapers.scraper_mediamarkt.MediaMarktScraper',
    'Vatan Bilgisayar': 'scrapers.scraper_vatanbilgisayar.VatanBilgisayarScraper',
    'Amazon TR': 'scrapers.scraper_amazon_tr.AmazonTRScraper',
    'N11': 'scrapers.scraper_n11.N11Scraper',
    'GittiGidiyor': 'scrapers.scraper_gittigidiyor.GittiGidiyorScraper',
    'Çiçeksepeti': 'scrapers.scraper_ciceksepeti.CiceksepetiScraper',
    'Morhipo': 'scrapers.scraper_morhipo.MorhipoScraper',
    'Genel Tedarikçi': 'scrapers.scraper_genel_tedarikci.GenelTedarikciScraper'
}

def get_active_suppliers(db_path="pazaryeri.db"):
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    cursor.execute("SELECT name FROM suppliers WHERE is_deleted = 0")
    suppliers = [row[0] for row in cursor.fetchall()]
    conn.close()
    return suppliers

def run_scraper(supplier_name, db_path="pazaryeri.db"):
    if supplier_name not in SUPPLIER_SCRAPERS:
        logger.warning(f"No scraper defined for supplier: {supplier_name}")
        return False
    
    try:
        module_name, class_name = SUPPLIER_SCRAPERS[supplier_name].rsplit('.', 1)
        module = importlib.import_module(module_name)
        scraper_class = getattr(module, class_name)
        
        logger.info(f"Starting scraper for {supplier_name}")
        scraper = scraper_class(db_path)
        scraper.scrape_all_products()
        logger.info(f"Completed scraping for {supplier_name}")
        return True
        
    except Exception as e:
        logger.error(f"Error running scraper for {supplier_name}: {str(e)}")
        return False

def main():
    if len(sys.argv) > 1:
        suppliers = sys.argv[1:]
        logger.info(f"Running scrapers for specified suppliers: {suppliers}")
    else:
        suppliers = get_active_suppliers()
        logger.info(f"Running scrapers for all active suppliers: {suppliers}")
    
    success_count = 0
    fail_count = 0
    
    for supplier in suppliers:
        if run_scraper(supplier):
            success_count += 1
        else:
            fail_count += 1
    
    logger.info(f"Price update completed. Success: {success_count}, Failed: {fail_count}")

if __name__ == "__main__":
    main()